= Crowdsales

All crowdsale-related contracts were removed from the OpenZeppelin Contracts library on the https://forum.openzeppelin.com/t/openzeppelin-contracts-v3-0-beta-release/2256[v3.0.0 release] due to both a decline in their usage and the complexity associated with migrating them to Solidity v0.6.

They are however still available on the v2.5 release of OpenZeppelin Contracts, which you can install by running:

```console
$ npm install @openzeppelin/contracts@v2.5
```

Refer to the https://docs.openzeppelin.com/contracts/2.x/crowdsales[v2.x documentation] when working with them.
