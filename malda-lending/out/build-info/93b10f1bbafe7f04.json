{"id": "93b10f1bbafe7f04", "source_id_to_path": {"0": "lib/forge-std/src/Base.sol", "1": "lib/forge-std/src/StdAssertions.sol", "2": "lib/forge-std/src/StdChains.sol", "3": "lib/forge-std/src/StdCheats.sol", "4": "lib/forge-std/src/StdConstants.sol", "5": "lib/forge-std/src/StdError.sol", "6": "lib/forge-std/src/StdInvariant.sol", "7": "lib/forge-std/src/StdJson.sol", "8": "lib/forge-std/src/StdMath.sol", "9": "lib/forge-std/src/StdStorage.sol", "10": "lib/forge-std/src/StdStyle.sol", "11": "lib/forge-std/src/StdToml.sol", "12": "lib/forge-std/src/StdUtils.sol", "13": "lib/forge-std/src/Test.sol", "14": "lib/forge-std/src/Vm.sol", "15": "lib/forge-std/src/console.sol", "16": "lib/forge-std/src/console2.sol", "17": "lib/forge-std/src/interfaces/IMulticall3.sol", "18": "lib/forge-std/src/safeconsole.sol", "19": "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "20": "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "21": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "22": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol", "23": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "24": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "25": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "26": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "27": "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "28": "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "29": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "30": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "31": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "32": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol", "33": "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "34": "lib/openzeppelin-contracts/contracts/utils/Address.sol", "35": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "36": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "37": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "38": "src/Operator/Operator.sol", "39": "src/Operator/OperatorStorage.sol", "40": "src/Roles.sol", "41": "src/blacklister/Blacklister.sol", "42": "src/interest/JumpRateModelV4.sol", "43": "src/interfaces/IBlacklister.sol", "44": "src/interfaces/IInterestRateModel.sol", "45": "src/interfaces/IOperator.sol", "46": "src/interfaces/IOracleOperator.sol", "47": "src/interfaces/IRewardDistributor.sol", "48": "src/interfaces/IRoles.sol", "49": "src/interfaces/ImErc20.sol", "50": "src/interfaces/ImToken.sol", "51": "src/interfaces/external/poh/IPohVerifier.sol", "52": "src/mToken/mErc20.sol", "53": "src/mToken/mErc20Immutable.sol", "54": "src/mToken/mToken.sol", "55": "src/mToken/mTokenConfiguration.sol", "56": "src/mToken/mTokenStorage.sol", "57": "src/rewards/RewardDistributor.sol", "58": "src/utils/ExponentialNoError.sol", "59": "test/Base_Unit_Test.t.sol", "60": "test/mocks/ERC20Mock.sol", "61": "test/mocks/OracleMock.sol", "62": "test/unit/UnboundedLoopDoSVulnerability.t.sol", "63": "test/utils/Constants.sol", "64": "test/utils/Events.sol", "65": "test/utils/Helpers.sol", "66": "test/utils/Types.sol"}, "language": "Solidity"}