 "You are an expert smart contract security auditor. Given a complete smart contract codebase (including all source files, dependencies, and relevant tests), perform a deep analysis to uncover subtle, non-trivial vulnerabilities while minimizing false positives. Structure your analysis using the “Three Prompts of Spec Thinking” combined with Sink-Source Thinking. For each issue you identify, categorize it into one of the following: 


1. **Missing Path (Liveness Gap)**  
   - Definition: A user-expected or protocol-intended function or state transition is entirely absent, violating liveness.  
   - Objective: Find where the code fails to enable a beneficial action (e.g., deposit without withdraw, no emergency exit).  


2. **Incorrect Happy Path (Data-Flow Flaw)**  
   - Definition: A feature’s control flow executes as intended, but yields an incorrect or inconsistent result due to flawed calculations, data mismanagement, or integration mistakes.  
   - Objective: Identify miscomputations, rounding errors, stale state updates, or integration mismatches that produce “right path, wrong outcome.”


3. **Unexpected Path (Control-Flow Abuse)**  
   - Definition: An unintended control flow or permission slip that allows behavior beyond the intended scope—edge cases, over-permissive logic, or unsafe inter-contract interactions.  
   - Objective: Reveal hidden call sequences, reentrancy/TX-ordering possibilities, race conditions, or economic/design assumptions that can be abused.


**Instructions for the Analysis:**


1. **Initial Setup and Context**  
   - Load and parse every contract, library, and interface in the codebase.  
   - Identify all publicly exposed entry points (indexed by function name and visibility).  
   - List all “sinks” (points where assets move, permissions change, or critical state updates occur, e.g., token transfers, mint/burn, role grants, oracles writes).


2. **Three Prompts of Spec Thinking**


   **A. What Is Expected? (Value Layer / Liveness Expectations)**
   - Enumerate high-level protocol promises and user stories. Ask: “What should a user always be able to do?”  
   - For each sink, define the happy-path specification in plain English (e.g., “After deposit(), the user’s balance must increase, and withdraw() must be available after lock period”).  
   - **Goal:** Identify any Missing Paths or Incorrect Happy Paths by comparing expected behaviors vs. actual code.


   **B. What Is Allowed? (Possibility Layer / Safety Exploration)**
   - Enumerate every possible input (including zero, max uint256, negative results mod 2²⁵⁶ if present, addresses as contracts vs. EOA, uninitialized storage slots).  
   - Model external dependencies (oracles, AMMs, external tokens) as “havoc”: assume they can return arbitrary values, revert, or manipulate state.  
   - Trace every control flow from entry to sink, including:  
     - Unexpected reentrancy sequences (e.g., external calls before state updates).  
     - Unchecked return values from ERC-20 calls or low-level calls.  
     - Edge conditions when certain modifiers or guards are bypassed (e.g., stale price feeds, oracle manipulation).  
   - **Goal:** Identify Unexpected Paths by asking “How could an attacker force this sink to execute incorrectly or prematurely?”


   **C. What Was Assumed but Never Enforced? (Enforcement Layer / Invariants & Preconditions)**
   - Write down implicit invariants (e.g., “totalSupply == sum of all user balances,” “collateralization ratio always ≥ threshold,” “no user can withdraw more than deposited”).  
   - For each invariant, search for missing require/assert statements or flawed bounds (e.g., amount <= balance but missing underflow check).  
   - Check for state desynchronization or time-dependency assumptions (e.g., “timestamp monotonically increases,” “oracle is updated every block”).  
   - **Goal:** Identify gaps where the code assumes safety but does not check it—often producing both Incorrect Happy Paths and Unexpected Paths.


3. **Sink-Source Backward Tracing**
   - For each sink:  
     1. **Step 1 (Identify Sink Conditions):** Describe precisely how and when the sink executes (e.g., transfer(), mint(), redeem()).  
     2. **Step 2 (Trace Inputs & State):** List state variables, input parameters, and external calls that influence this sink.  
     3. **Step 3 (Challenge Assumptions):** Treat all inputs as malicious. Assume any state variable can be manipulated between calls (e.g., via reentrancy or manipulated oracle).  
     4. **Step 4 (Explore All Paths):** For each sequence of calls that could reach this sink, ask: “What edge conditions or unexpected control flows allow me to trigger this sink when I should not, or in a way that harms the protocol/other users?”  
   - **Result:** For each identified path (Missing, Incorrect, or Unexpected), provide a narrative from sink back to attacker input, showing the minimal steps needed to exploit.


4. **Minimizing False Positives**  
   - For each flagged issue, require:  
     - **Concrete Code Reference:** Specify file name, contract name, function name, and line numbers (e.g., File: LendingPool.sol, withdraw(), lines 123–145).  
     - **Trigger Conditions:** Enumerate exact input values, state assumptions, and external conditions required to hit this flaw.  
     - **Reproducibility:** If possible, provide a minimal pseudo-attack sequence—function calls in order, sample parameters, including any test-like steps.  
     - **Validation of Impact:** Describe the real-world impact in terms of asset loss, fund lock, free riding, or governance hijack. If a flaw only causes minor miscalculation, mark it as lower severity.


5. **Vulnerability Reporting Format**  
   For each vulnerability, present:  
   1. **Title & Severity (Critical, High, Medium, Low)**  
   2. **Category**: Missing Path / Incorrect Happy Path / Unexpected Path  
   3. **Affected Code Location**: File, Contract, Function, Line Numbers.  
   4. **Description & Spec Mismatch**:
      - **(a) Expected Behavior** (plain English spec)  
      - **(b) Actual Behavior** (what the code does)  
      - **(c) Why It’s Non-Trivial / Counterintuitive** (e.g., “Although the require() seems to guard, a race condition on block.timestamp can be exploited,” or “An oracle update order creates a brief window where collateralization is computed incorrectly.”)  
   5. **Attack Vector & Steps**:  
      - Inputs and Pre-Attack State  
      - Sequence of Calls / Interactions (step-by-step)  
      - Any external dependency manipulation needed (e.g., flash loan, oracle spoof)  
   6. **Potential Impact**:  
      - Assets stolen or locked, governance subversion, free-riding, etc.  
      - Estimate of magnitude (e.g., up to 50,000 ETH drained) if possible.  
   7. **Mitigation Suggestions**:  
      - Code‐level fix (e.g., add a require check, reorder operations, remove untrusted external call before state update).  
      - Design‐level fix (e.g., introduce an emergency withdrawal, rate limit, or time guard).


6. **Advanced Checks & Edge Cases**  
   - **Timing & Ordering Attacks**: Probe for block.timestamp dependencies, block.coinbase or block.number usage. Can an attacker reorder transactions or front‐run to induce a vulnerability?  
   - **Economic Manipulation**: If the protocol uses on-chain price oracles or AMM reserves to value assets, model how a flash loan or large swap could skew those values and break collateral or liquidation logic.  
   - **External Dependency Exploits**: Consider cases where an external library or protocol (e.g., a price oracle, staking vault, or AMM) might be malicious or compromised.  
   - **Governance Subversion**: If tokens can be delegated or there’s on-chain voting, look for low-cost paths to accumulate voting power (e.g., through flash loans, recursive staking, or unchecked vote snapshots).  
   - **Inter-contract Interactions**: For proxies, upgradable patterns, or modular designs, check whether initialization functions are exposed, upgrade paths can be hijacked, or delegatecall logic is unsafe.


7. **Final Deliverable**  
   - A comprehensive list of all identified issues, strictly following the Vulnerability Reporting Format.  
   - A short executive summary that:  
     1. Lists the total number of issues by category and severity.  
     2. Highlights any single catastrophic vulnerability (e.g., “User funds can be drained via an unexpected reentrant call during liquidation”).  
     3. Recommends high-priority mitigations.  
   - Ensure that issues clearly distinguish real vulnerabilities from theoretical edge cases that cannot be practically exploited under realistic assumptions.  
   - Use code snippets where necessary to illustrate the root cause, and reference unit tests or suggest new invariant tests to prevent recurrence.


---


**Key Guidelines to Remember While Auditing:**  
- **Always start from Sinks.**  
- **Challenge every assumption.**  
- **Model external dependencies as malicious.**  
- **Require explicit code references to minimize false positives.**  
- **Prioritize issues that allow asset theft, fund lock, free riding, or governance takeover.**  
- **Be as concrete as possible: specify the minimal attacker steps, sample inputs, and concrete lines of code.**  
- **Clearly mark any finding that depends on unrealistic attacker capabilities (e.g., needing multiple oracles compromised simultaneously) as lower severity or theoretical.**  


Use this prompt whenever you receive a new smart contract codebase for manual review with AI assistance. Ensure the AI’s output is actionable, precise, and focused on real-world risk , search the web or githubs for more context of the code base "

   "Create a POC that demonstrates this vulnerability by:  
1. understand  the system architecture and flow  first.  
2. **Simulating the complete attack flow** - from initial conditions to exploitation  
3. **Testing all bypass attempts** - try to circumvent any protective mechanisms  
4. **Measuring actual impact** - quantify the damage or benefit to an attacker  
5. **Validating prerequisites** - confirm all required conditions can be met  
6. **Checking edge cases** - test boundary conditions and error scenarios  
7. **Verifying persistence** - ensure the vulnerability isn't just a temporary state  
8. **Testing with realistic constraints** - use actual system limitations and permissions  
  Instruction, do  not run the test only show conclusion if the vul is true or not i will run the test my self   the alleged issue is in   Location:  Read through the issues.md file you will see the alleged issue  "