// PROOF OF CONCEPT: Malda Reorg Protection Vulnerability
// WARNING: This is for demonstration purposes only - DO NOT RUN IN PRODUCTION
//
// This POC demonstrates how the hardcoded reorg protection depths in Malda's
// ZK coprocessor can be exploited during network stress periods.

use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};

// Simulated constants from the actual Malda codebase
const REORG_PROTECTION_DEPTH_ETHEREUM: u64 = 2;
const REORG_PROTECTION_DEPTH_OPTIMISM: u64 = 2;
const REORG_PROTECTION_DEPTH_BASE: u64 = 2;
const REORG_PROTECTION_DEPTH_LINEA: u64 = 2;

// Chain IDs
const ETHEREUM_CHAIN_ID: u64 = 1;
const OPTIMISM_CHAIN_ID: u64 = 10;
const BASE_CHAIN_ID: u64 = 8453;
const LINEA_CHAIN_ID: u64 = 59144;

#[derive(Debug, <PERSON><PERSON>)]
struct NetworkConditions {
    uncle_rate: f64,           // Percentage of uncle blocks
    recent_reorgs: Vec<u64>,   // Recent reorg depths
    consensus_issues: bool,     // Active consensus problems
    gas_price_gwei: u64,       // Current gas price
    validator_participation: f64, // Validator participation rate
}

#[derive(Debug, Clone)]
struct BlockInfo {
    number: u64,
    hash: String,
    timestamp: u64,
    is_reorganized: bool,
}

#[derive(Debug)]
struct VulnerabilityPOC {
    network_conditions: HashMap<u64, NetworkConditions>,
    block_history: HashMap<u64, Vec<BlockInfo>>,
}

impl VulnerabilityPOC {
    fn new() -> Self {
        Self {
            network_conditions: HashMap::new(),
            block_history: HashMap::new(),
        }
    }

    // Simulate the vulnerable get_reorg_protection_depth function
    fn get_reorg_protection_depth(&self, chain_id: u64) -> u64 {
        match chain_id {
            ETHEREUM_CHAIN_ID => REORG_PROTECTION_DEPTH_ETHEREUM,
            OPTIMISM_CHAIN_ID => REORG_PROTECTION_DEPTH_OPTIMISM,
            BASE_CHAIN_ID => REORG_PROTECTION_DEPTH_BASE,
            LINEA_CHAIN_ID => REORG_PROTECTION_DEPTH_LINEA,
            _ => panic!("Invalid chain ID"),
        }
    }

    // Simulate network monitoring
    fn monitor_network_conditions(&mut self, chain_id: u64) -> NetworkConditions {
        // Simulate stressed network conditions that would make 2-block depth insufficient
        let conditions = NetworkConditions {
            uncle_rate: 8.5,  // High uncle rate indicating network stress
            recent_reorgs: vec![3, 2, 4, 2, 5], // Recent reorgs deeper than protection
            consensus_issues: true,
            gas_price_gwei: 150, // High gas indicating congestion
            validator_participation: 92.3, // Slightly low participation
        };
        
        self.network_conditions.insert(chain_id, conditions.clone());
        conditions
    }

    // Simulate the vulnerability window detection
    fn detect_vulnerability_window(&self, chain_id: u64) -> bool {
        if let Some(conditions) = self.network_conditions.get(&chain_id) {
            // Vulnerability conditions:
            // 1. High uncle rate (>5%)
            // 2. Recent reorgs deeper than protection depth
            // 3. Active consensus issues
            let high_uncle_rate = conditions.uncle_rate > 5.0;
            let deep_recent_reorgs = conditions.recent_reorgs.iter()
                .any(|&depth| depth > self.get_reorg_protection_depth(chain_id));
            let consensus_problems = conditions.consensus_issues;
            
            high_uncle_rate && deep_recent_reorgs && consensus_problems
        } else {
            false
        }
    }

    // Simulate proof generation with vulnerable reorg protection
    fn simulate_vulnerable_proof_generation(&self, chain_id: u64, current_block: u64) -> Result<String, String> {
        let protection_depth = self.get_reorg_protection_depth(chain_id);
        let target_block = current_block - protection_depth;
        
        println!("🔍 Generating proof for chain {} at block {}", chain_id, current_block);
        println!("📊 Using reorg protection depth: {} blocks", protection_depth);
        println!("🎯 Target block for proof: {}", target_block);
        
        // Check if we're in a vulnerability window
        if self.detect_vulnerability_window(chain_id) {
            println!("⚠️  WARNING: Network conditions indicate vulnerability window!");
            println!("   - High uncle rate detected");
            println!("   - Recent deep reorgs observed");
            println!("   - Consensus issues active");
            println!("   - 2-block protection may be insufficient");
            
            return Ok(format!("VULNERABLE_PROOF_{}_{}", chain_id, target_block));
        }
        
        Ok(format!("NORMAL_PROOF_{}_{}", chain_id, target_block))
    }

    // Simulate the attack scenario
    fn simulate_attack_scenario(&mut self) -> Result<(), String> {
        println!("🚨 SIMULATING REORG VULNERABILITY ATTACK SCENARIO");
        println!("{}", "=".repeat(60));
        
        // Step 1: Monitor network conditions
        println!("\n📡 Step 1: Monitoring network conditions...");
        let eth_conditions = self.monitor_network_conditions(ETHEREUM_CHAIN_ID);
        let opt_conditions = self.monitor_network_conditions(OPTIMISM_CHAIN_ID);
        
        println!("Ethereum conditions: {:?}", eth_conditions);
        println!("Optimism conditions: {:?}", opt_conditions);
        
        // Step 2: Detect vulnerability windows
        println!("\n🔍 Step 2: Detecting vulnerability windows...");
        let eth_vulnerable = self.detect_vulnerability_window(ETHEREUM_CHAIN_ID);
        let opt_vulnerable = self.detect_vulnerability_window(OPTIMISM_CHAIN_ID);
        
        println!("Ethereum vulnerable: {}", eth_vulnerable);
        println!("Optimism vulnerable: {}", opt_vulnerable);
        
        if !eth_vulnerable && !opt_vulnerable {
            return Err("No vulnerability windows detected".to_string());
        }
        
        // Step 3: Simulate proof generation during vulnerability window
        println!("\n⚡ Step 3: Generating proofs during vulnerability window...");
        
        if eth_vulnerable {
            let proof = self.simulate_vulnerable_proof_generation(ETHEREUM_CHAIN_ID, 19000000)?;
            println!("Generated Ethereum proof: {}", proof);
        }
        
        if opt_vulnerable {
            let proof = self.simulate_vulnerable_proof_generation(OPTIMISM_CHAIN_ID, 115000000)?;
            println!("Generated Optimism proof: {}", proof);
        }
        
        // Step 4: Simulate the reorg that invalidates the proof
        println!("\n🔄 Step 4: Simulating network reorganization...");
        self.simulate_network_reorg(ETHEREUM_CHAIN_ID, 4); // 4-block reorg
        
        println!("✅ POC completed successfully!");
        println!("\n📋 VULNERABILITY SUMMARY:");
        println!("- Hardcoded 2-block reorg protection is insufficient during network stress");
        println!("- Attack window exists when networks experience >2 block reorgs");
        println!("- Proofs can be generated using blocks that later get reorganized");
        println!("- Cross-chain state inconsistencies can be exploited");
        
        Ok(())
    }

    // Simulate a network reorganization
    fn simulate_network_reorg(&mut self, chain_id: u64, reorg_depth: u64) {
        println!("🔄 Simulating {}-block reorganization on chain {}", reorg_depth, chain_id);
        
        let protection_depth = self.get_reorg_protection_depth(chain_id);
        if reorg_depth > protection_depth {
            println!("💥 CRITICAL: Reorg depth ({}) exceeds protection depth ({})!", 
                     reorg_depth, protection_depth);
            println!("   This would invalidate proofs that appeared valid!");
        }
        
        // Simulate blocks being reorganized
        let mut blocks = Vec::new();
        for i in 0..reorg_depth {
            blocks.push(BlockInfo {
                number: 19000000 - i,
                hash: format!("0xreorg_{}", i),
                timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
                is_reorganized: true,
            });
        }
        
        self.block_history.insert(chain_id, blocks);
    }

    // Demonstrate the fix (what should be implemented)
    fn demonstrate_proper_reorg_protection(&self, chain_id: u64) -> u64 {
        println!("\n🛡️  DEMONSTRATING PROPER REORG PROTECTION:");
        
        // This is what the fix should look like
        let base_depth = match chain_id {
            ETHEREUM_CHAIN_ID => 6,  // Increased base depth
            OPTIMISM_CHAIN_ID => 4,  // L2s need different depths
            BASE_CHAIN_ID => 4,
            LINEA_CHAIN_ID => 4,
            _ => 6,
        };
        
        // Dynamic adjustment based on network conditions
        let dynamic_adjustment = if let Some(conditions) = self.network_conditions.get(&chain_id) {
            let mut adjustment = 0;
            
            if conditions.uncle_rate > 5.0 { adjustment += 2; }
            if conditions.consensus_issues { adjustment += 3; }
            if conditions.recent_reorgs.iter().any(|&d| d > base_depth) { adjustment += 2; }
            
            adjustment
        } else {
            0
        };
        
        let final_depth = base_depth + dynamic_adjustment;
        
        println!("Base depth: {}", base_depth);
        println!("Dynamic adjustment: +{}", dynamic_adjustment);
        println!("Final protection depth: {}", final_depth);
        
        final_depth
    }
}

// Main demonstration function
fn main() -> Result<(), String> {
    println!("🔬 MALDA REORG PROTECTION VULNERABILITY POC");
    println!("==========================================");
    println!("This POC demonstrates the vulnerability without executing any attacks.");
    println!();
    
    let mut poc = VulnerabilityPOC::new();
    
    // Run the attack simulation
    poc.simulate_attack_scenario()?;
    
    // Demonstrate the proper fix
    println!("\n{}", "=".repeat(60));
    poc.demonstrate_proper_reorg_protection(ETHEREUM_CHAIN_ID);
    
    println!("\n⚠️  DISCLAIMER: This is a proof-of-concept for security research only.");
    println!("   Do not use this code for malicious purposes.");
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_vulnerability_detection() {
        let mut poc = VulnerabilityPOC::new();
        poc.monitor_network_conditions(ETHEREUM_CHAIN_ID);
        assert!(poc.detect_vulnerability_window(ETHEREUM_CHAIN_ID));
    }

    #[test]
    fn test_reorg_protection_insufficient() {
        let poc = VulnerabilityPOC::new();
        let protection = poc.get_reorg_protection_depth(ETHEREUM_CHAIN_ID);
        assert_eq!(protection, 2); // Current vulnerable value
        
        // Simulate conditions where 2 blocks is insufficient
        assert!(protection < 4); // Historical reorgs have been deeper
    }
}
