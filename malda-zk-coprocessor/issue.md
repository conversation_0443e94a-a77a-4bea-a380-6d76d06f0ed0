Issue Confirmed: Missing R Component Validation
The reported vulnerability is valid. The recover_signer function in the Malda protocol contains a critical security flaw where it only validates the s component of ECDSA signatures but completely omits validation of the r component.

Root Cause Analysis
The vulnerability exists in the signature validation logic where only the s component is checked against the curve order bounds: cryptography.rs:72-74

However, the r component is copied directly into the signature buffer without any bounds validation: cryptography.rs:78

According to ECDSA standards, both signature components must satisfy: r, s ∈ [1, n-1] where n is the secp256k1 curve order. The current implementation allows any 256-bit value for r, including values ≥ n.

Impact Assessment
This vulnerability has critical impact because the recover_signer function is used in sequencer authentication for the Malda protocol: validators.rs:768-769

The sequencer validation process relies on signature recovery to verify block authenticity, and signature malleability can allow attackers to forge signatures that recover to different addresses while still appearing valid.

Proof of Concept
The attack works as follows:

Obtain Valid Signature: Attacker gets a legitimate signature (r, s, v) from an honest signer
Craft Malformed R: Compute r' = r + n where n is the secp256k1 curve order
Bypass Validation: The malformed signature (r', s, v) passes validation because:
Only s is checked against SECP256K1N_HALF constants.rs:84-87
The r' component is copied without validation
Internal Reduction: The k256 library internally reduces r' mod n = r, making the signature mathematically valid but recovering to a different public key
Technical Details
The secp256k1 curve order defines the valid range for signature components. The constant SECP256K1N_HALF represents half the curve order: constants.rs:80-87

The vulnerability allows signatures with r ≥ n, which the k256 library processes via modular reduction in the recover_signer_unchecked function: cryptography.rs:103

Recommended Mitigation
Add validation for the r component before signature processing:

pub fn recover_signer(signature: Signature, sighash: B256) -> Option<Address> {  
    // Validate both r and s components are in valid range [1, n-1]  
    if signature.r().is_zero() || signature.r() >= SECP256K1N ||   
       signature.s().is_zero() || signature.s() > SECP256K1N_HALF {  
        return None;  
    }  
    // ... rest of function  
}
Notes
This vulnerability affects the core cryptographic security of the Malda protocol's cross-chain validation system. The missing bounds check on the r component violates ECDSA security standards and enables signature malleability attacks that could compromise sequencer authentication. The issue is particularly critical because it's used in the block validation pipeline where sequencer identity verification is essential for protocol security.

The attack requires no special preconditions beyond obtaining a valid signature, making it easily exploitable by malicious actors. The unlimited impact potential includes the ability to forge sequencer signatures and potentially manipulate cross-chain proof submissions.