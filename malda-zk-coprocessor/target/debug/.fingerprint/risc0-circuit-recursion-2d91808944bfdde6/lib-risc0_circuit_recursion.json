{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"cuda\", \"default\", \"metal\", \"prove\", \"risc0-circuit-recursion-sys\", \"std\", \"test\"]", "target": 744416552149155838, "profile": 2241668132362809309, "path": 11457346955893701835, "deps": [[2358608249731162897, "risc0_zkp", false, 16377063629553783524], [6511429716036861196, "bytemuck", false, 5970047994285344772], [7264961447180378050, "risc0_core", false, 15535715492867967995], [8606274917505247608, "tracing", false, 15530016278230585856], [13625485746686963219, "anyhow", false, 9879049321265876028], [16097111343857175931, "build_script_build", false, 3416010554586703723]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/risc0-circuit-recursion-2d91808944bfdde6/dep-lib-risc0_circuit_recursion", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}