{"rustc": 13226066032359371072, "features": "[\"default\", \"std\"]", "declared_features": "[\"circuit_debug\", \"cuda\", \"default\", \"metal\", \"metal_prefix_products\", \"prove\", \"std\", \"unstable\"]", "target": 11561838669794982184, "profile": 2241668132362809309, "path": 13956872161073377063, "deps": [[530211389790465181, "hex", false, 10292843913979569116], [2358608249731162897, "build_script_build", false, 2615131108731958327], [2828590642173593838, "cfg_if", false, 12379903088264604467], [6203123018298125816, "borsh", false, 10899872228502063845], [6511429716036861196, "bytemuck", false, 5970047994285344772], [7264961447180378050, "risc0_core", false, 15535715492867967995], [8606274917505247608, "tracing", false, 15530016278230585856], [8632578124021956924, "hex_literal", false, 11671733663372065456], [8700459469608572718, "blake2", false, 2508801306179264218], [9689903380558560274, "serde", false, 13902469085514894929], [9857275760291862238, "sha2", false, 4002209552109299503], [11109039379400878162, "risc0_zkvm_platform", false, 3141582324925199150], [11115194146618580017, "stability", false, 2747746342742252058], [13625485746686963219, "anyhow", false, 9879049321265876028], [17475753849556516473, "digest", false, 140149734445324485], [17605717126308396068, "paste", false, 8483729510288934481], [18130209639506977569, "rand_core", false, 2294235732715420626]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/risc0-zkp-9a75ae20acb8192d/dep-lib-risc0_zkp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}