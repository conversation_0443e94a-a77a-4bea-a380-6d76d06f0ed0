{"rustc": 13226066032359371072, "features": "[\"allocator-api2\", \"default-hasher\", \"inline-more\", \"serde\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2241668132362809309, "path": 4527459610340134764, "deps": [[9150530836556604396, "allocator_api2", false, 8364486777011237071], [9689903380558560274, "serde", false, 13902469085514894929], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 12330852176309447324]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-7cf8a7372294cc87/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}