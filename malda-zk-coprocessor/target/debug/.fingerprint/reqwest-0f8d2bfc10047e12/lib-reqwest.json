{"rustc": 13226066032359371072, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"multipart\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 3770542644202194360, "deps": [[40386456601120721, "percent_encoding", false, 13100953575333840213], [418947936956741439, "h2", false, 8328568675978071019], [778154619793643451, "hyper_util", false, 12079165127264072383], [784494742817713399, "tower_service", false, 12193628328957816398], [1288403060204016458, "tokio_util", false, 483955828098588801], [1788832197870803419, "hyper_rustls", false, 9234704019185280400], [1811549171721445101, "futures_channel", false, 4378578920957365966], [1906322745568073236, "pin_project_lite", false, 3192898420806307456], [2054153378684941554, "tower_http", false, 11182378921888132485], [2517136641825875337, "sync_wrapper", false, 16815462370192863530], [2883436298747778685, "rustls_pki_types", false, 10572488550370123930], [3150220818285335163, "url", false, 14930828032240483960], [4942430025333810336, "webpki_roots", false, 16841591848729428762], [5695049318159433696, "tower", false, 3076929767313475886], [5986029879202738730, "log", false, 1690976416000601508], [7620660491849607393, "futures_core", false, 1559213636597315443], [9010263965687315507, "http", false, 17824218619973047208], [9538054652646069845, "tokio", false, 583227580819415231], [9689903380558560274, "serde", false, 2560059485084922610], [10229185211513642314, "mime", false, 6699806921528225474], [10629569228670356391, "futures_util", false, 11880514652847822067], [11895591994124935963, "tokio_rustls", false, 947057689250613699], [11957360342995674422, "hyper", false, 6864499133546707206], [12186126227181294540, "tokio_native_tls", false, 18253523069916126935], [13077212702700853852, "base64", false, 5259858394099443345], [14084095096285906100, "http_body", false, 14111015421628655764], [14564311161534545801, "encoding_rs", false, 5243805418299428991], [15367738274754116744, "serde_json", false, 15252519466085847446], [16066129441945555748, "bytes", false, 17791678820998801156], [16400140949089969347, "rustls", false, 11276471013002673777], [16542808166767769916, "serde_urlencoded", false, 1270566881896550072], [16785601910559813697, "native_tls_crate", false, 5497874130712142210], [16900715236047033623, "http_body_util", false, 16490639973920481006], [18071510856783138481, "mime_guess", false, 8677669185540382981], [18273243456331255970, "hyper_tls", false, 2360240104945044496]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-0f8d2bfc10047e12/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}