{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"aws-smithy-runtime\", \"examples\"]", "target": 6854809040781774872, "profile": 15657897354478470176, "path": 2191273206290686643, "deps": [[597652819505411233, "aws_smithy_async", false, 5289235676388137859], [628335450098706537, "build_script_build", false, 8998037188246179954], [8606274917505247608, "tracing", false, 6942084126805219901], [8732774447830947144, "aws_credential_types", false, 18182587312467301437], [10535238618256657085, "aws_smithy_types", false, 18027050269860777449], [16503430488003824554, "aws_smithy_runtime_api", false, 11181949005332787390]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/aws-types-fc1985e1eb7b86f8/dep-lib-aws_types", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}