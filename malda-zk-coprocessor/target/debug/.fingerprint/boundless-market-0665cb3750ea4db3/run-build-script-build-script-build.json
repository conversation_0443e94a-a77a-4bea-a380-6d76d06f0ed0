{"rustc": 13226066032359371072, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[7238338081869314361, "build_script_build", false, 1628660452014994017]], "local": [{"RerunIfChanged": {"output": "debug/build/boundless-market-0665cb3750ea4db3/output", "paths": ["build.rs", "src/contracts/artifacts"]}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_OS", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_MANIFEST_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_OS", "val": null}}, {"RerunIfEnvChanged": {"var": "OUT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_OS", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_OS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}