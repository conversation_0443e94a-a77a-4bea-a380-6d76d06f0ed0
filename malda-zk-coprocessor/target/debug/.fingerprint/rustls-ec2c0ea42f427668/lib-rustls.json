{"rustc": 13226066032359371072, "features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 3506616335609839501, "deps": [[2883436298747778685, "pki_types", false, 10572488550370123930], [3722963349756955755, "once_cell", false, 12396378922497620399], [5491919304041016563, "ring", false, 6864060046567866134], [6528079939221783635, "zeroize", false, 4534150598234199993], [10099379384483871283, "aws_lc_rs", false, 9301567633356441328], [16400140949089969347, "build_script_build", false, 16484386593025955211], [17003143334332120809, "subtle", false, 14398310319331809946], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 8989821464534773295]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-ec2c0ea42f427668/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}