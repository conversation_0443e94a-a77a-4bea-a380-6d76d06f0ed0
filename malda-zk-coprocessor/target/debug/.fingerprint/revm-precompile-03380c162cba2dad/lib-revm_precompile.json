{"rustc": 13226066032359371072, "features": "[\"secp256r1\", \"std\"]", "declared_features": "[\"asm-keccak\", \"blst\", \"bn\", \"c-kzg\", \"default\", \"hashbrown\", \"kzg-rs\", \"libsecp256k1\", \"portable\", \"secp256k1\", \"secp256r1\", \"std\"]", "target": 15028308191872412523, "profile": 2915461960415894863, "path": 1208036099013853830, "deps": [[647417929892486539, "ark_serialize", false, 7082038868372887304], [2828590642173593838, "cfg_if", false, 12379903088264604467], [3434989764622224963, "k256", false, 7973187018455897827], [3722963349756955755, "once_cell", false, 8076464963361065281], [5502062331616315784, "ark_ff", false, 15569003004039897809], [6151811949586245694, "ark_bn254", false, 13377958202017431209], [9857275760291862238, "sha2", false, 4002209552109299503], [14539391407805927429, "primitives", false, 9201967310633738568], [15377193432756420161, "p256", false, 9053175459857336041], [15583278516016338073, "ark_bls12_381", false, 4591413996921432274], [15603583605579657406, "ripemd", false, 2976395938678733010], [15963096839140239429, "aurora_engine_modexp", false, 588857002839357831], [17532637862849517517, "ark_ec", false, 14550774034805136563]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/revm-precompile-03380c162cba2dad/dep-lib-revm_precompile", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}