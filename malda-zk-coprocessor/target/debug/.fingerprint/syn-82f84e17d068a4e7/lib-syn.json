{"rustc": 13226066032359371072, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 14995896139824458418, "deps": [[1988483478007900009, "unicode_ident", false, 17964298410072591102], [3060637413840920116, "proc_macro2", false, 11978631202661594756], [17990358020177143287, "quote", false, 4968544993622710939]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-82f84e17d068a4e7/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}