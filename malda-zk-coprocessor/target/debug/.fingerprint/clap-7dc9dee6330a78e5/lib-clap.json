{"rustc": 13226066032359371072, "features": "[\"color\", \"default\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15221872889701672926, "path": 5004659528838781961, "deps": [[1457576002496728321, "clap_derive", false, 17122444464087521189], [7361794428713524931, "clap_builder", false, 16317905804050566273]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-7dc9dee6330a78e5/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}