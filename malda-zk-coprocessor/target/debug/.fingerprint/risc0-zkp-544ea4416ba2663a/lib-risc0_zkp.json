{"rustc": 13226066032359371072, "features": "[\"default\", \"std\"]", "declared_features": "[\"circuit_debug\", \"cuda\", \"default\", \"metal\", \"metal_prefix_products\", \"prove\", \"std\", \"unstable\"]", "target": 11561838669794982184, "profile": 15657897354478470176, "path": 13956872161073377063, "deps": [[530211389790465181, "hex", false, 3379478178411117322], [2358608249731162897, "build_script_build", false, 2615131108731958327], [2828590642173593838, "cfg_if", false, 1903128207448990402], [6203123018298125816, "borsh", false, 17887231629342253297], [6511429716036861196, "bytemuck", false, 18095207708388160398], [7264961447180378050, "risc0_core", false, 16839565789038798930], [8606274917505247608, "tracing", false, 12546630441409337383], [8632578124021956924, "hex_literal", false, 3006963861266844182], [8700459469608572718, "blake2", false, 12107193226193570049], [9689903380558560274, "serde", false, 6295511631161797798], [9857275760291862238, "sha2", false, 6333067065029443388], [11109039379400878162, "risc0_zkvm_platform", false, 15470133214853510609], [11115194146618580017, "stability", false, 2747746342742252058], [13625485746686963219, "anyhow", false, 5527867187803302802], [17475753849556516473, "digest", false, 16103309049199330995], [17605717126308396068, "paste", false, 8483729510288934481], [18130209639506977569, "rand_core", false, 7904627277969005119]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/risc0-zkp-544ea4416ba2663a/dep-lib-risc0_zkp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}