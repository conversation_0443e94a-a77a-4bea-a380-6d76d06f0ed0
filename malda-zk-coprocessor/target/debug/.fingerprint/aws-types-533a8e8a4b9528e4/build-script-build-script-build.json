{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"aws-smithy-runtime\", \"examples\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 16280282639839514991, "deps": [[8576480473721236041, "rustc_version", false, 6286962478990602844]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/aws-types-533a8e8a4b9528e4/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}