{"rustc": 13226066032359371072, "features": "[\"providers\"]", "declared_features": "[\"default\", \"providers\"]", "target": 12066720079533700665, "profile": 2225463790103693989, "path": 15785531937388889585, "deps": [[2141440161325128830, "ethers_contract_abigen", false, 9222963794760367794], [3060637413840920116, "proc_macro2", false, 9034550061788385353], [5030409040180886167, "ethers_core", false, 8375041452982567998], [10273615881155074728, "inflector", false, 14984962067673909192], [10640660562325816595, "syn", false, 10012784794122688946], [13003293521383684806, "hex", false, 14456220117032510742], [15367738274754116744, "serde_json", false, 12736638636832762270], [17990358020177143287, "quote", false, 6802304478166651698]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ethers-contract-derive-f6deea07db7785d9/dep-lib-ethers_contract_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}