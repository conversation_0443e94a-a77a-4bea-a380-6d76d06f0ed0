{"rustc": 13226066032359371072, "features": "[\"alloc\", \"derive\", \"digest\", \"identity\", \"multihash-derive\", \"multihash-impl\", \"sha-2\", \"sha2\", \"std\"]", "declared_features": "[\"alloc\", \"arb\", \"arbitrary\", \"blake2b\", \"blake2b_simd\", \"blake2s\", \"blake2s_simd\", \"blake3\", \"default\", \"derive\", \"digest\", \"identity\", \"multihash-derive\", \"multihash-impl\", \"parity-scale-codec\", \"quickcheck\", \"rand\", \"ripemd\", \"ripemd-rs\", \"scale-codec\", \"secure-hashes\", \"serde\", \"serde-big-array\", \"serde-codec\", \"sha-1\", \"sha-2\", \"sha-3\", \"sha1\", \"sha2\", \"sha3\", \"std\", \"strobe\", \"strobe-rs\"]", "target": 5862592012661536641, "profile": 15657897354478470176, "path": 13919309103600739897, "deps": [[9857275760291862238, "sha_2", false, 3986058278091560241], [12414424756982115322, "core2", false, 12831348202253290236], [12499447783581773206, "multihash_derive", false, 13882095452576631971], [16581573552258847347, "unsigned_varint", false, 6640653433319935906], [17475753849556516473, "digest", false, 11922288272037430477]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/multihash-95b74667db80d5a6/dep-lib-multihash", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}