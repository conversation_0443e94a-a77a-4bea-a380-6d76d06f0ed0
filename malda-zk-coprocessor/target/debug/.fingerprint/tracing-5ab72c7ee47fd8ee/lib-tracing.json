{"rustc": 13226066032359371072, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 6355579909791343455, "path": 4881087286698293964, "deps": [[1906322745568073236, "pin_project_lite", false, 3192898420806307456], [3424551429995674438, "tracing_core", false, 4232886703490372862], [5986029879202738730, "log", false, 1408422961200230631], [15574202673389706213, "tracing_attributes", false, 11297205105478166165]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-5ab72c7ee47fd8ee/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}