{"rustc": 13226066032359371072, "features": "[\"default\", \"k256\", \"serde\", \"std\"]", "declared_features": "[\"arbitrary\", \"default\", \"k256\", \"kzg\", \"secp256k1\", \"serde\", \"serde-bincode-compat\", \"serde_with\", \"std\"]", "target": 2929293942009581624, "profile": 1455982124524106486, "path": 17265317210944172886, "deps": [[3434989764622224963, "k256", false, 7973187018455897827], [3722963349756955755, "once_cell", false, 8076464963361065281], [4552780081777059061, "alloy_primitives", false, 1708916969974984938], [5597578105680351444, "alloy_rlp", false, 12160384680694605826], [7113539788457516621, "alloy_trie", false, 11294306347898033142], [8844834803788054349, "alloy_eips", false, 7134968047005548121], [9689903380558560274, "serde", false, 13902469085514894929], [9890037079353029638, "alloy_serde", false, 5934534825539963960], [10806645703491011684, "thiserror", false, 16435724550757905811], [11293676373856528358, "derive_more", false, 6031128645935574517], [12170264697963848012, "either", false, 17835556121694348513], [18125022703902813197, "auto_impl", false, 7779807868399602672]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-consensus-ff555e5339874a68/dep-lib-alloy_consensus", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}