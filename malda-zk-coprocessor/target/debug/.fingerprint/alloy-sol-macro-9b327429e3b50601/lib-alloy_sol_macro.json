{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"json\"]", "target": 11427984703986570548, "profile": 8132301521667693190, "path": 16329957784596888544, "deps": [[3060637413840920116, "proc_macro2", false, 11978631202661594756], [4683367673802423867, "alloy_sol_macro_input", false, 12885161707589403239], [7537596097170821116, "alloy_sol_macro_expander", false, 13151758567978393912], [10640660562325816595, "syn", false, 15938410596040983936], [15755541468655779741, "proc_macro_error2", false, 3554993079719770613], [17990358020177143287, "quote", false, 4968544993622710939]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-sol-macro-9b327429e3b50601/dep-lib-alloy_sol_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}