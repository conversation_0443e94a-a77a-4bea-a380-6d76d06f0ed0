{"rustc": 13226066032359371072, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2241668132362809309, "path": 1292604811581334034, "deps": [[4022439902832367970, "zerofrom_derive", false, 1789981310542748505]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zerofrom-a8c63fb1c1c4ee7a/dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}