{"rustc": 13226066032359371072, "features": "[\"alloc\", \"arithmetic\", \"default\", \"digest\", \"ecdsa\", \"ecdsa-core\", \"expose-field\", \"once_cell\", \"pkcs8\", \"precomputed-tables\", \"schnorr\", \"serde\", \"serdect\", \"sha2\", \"sha256\", \"signature\", \"std\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"bits\", \"critical-section\", \"default\", \"digest\", \"ecdh\", \"ecdsa\", \"ecdsa-core\", \"expose-field\", \"hash2curve\", \"hex-literal\", \"jwk\", \"once_cell\", \"pem\", \"pkcs8\", \"precomputed-tables\", \"schnorr\", \"serde\", \"serdect\", \"sha2\", \"sha256\", \"signature\", \"std\", \"test-vectors\"]", "target": 2074457694779954094, "profile": 2241668132362809309, "path": 11286597672375728731, "deps": [[2348975382319678783, "ecdsa_core", false, 12334517525328025854], [2828590642173593838, "cfg_if", false, 12379903088264604467], [3722963349756955755, "once_cell", false, 8076464963361065281], [5844362839343846847, "serdect", false, 18029600520281923744], [9857275760291862238, "sha2", false, 4002209552109299503], [10149501514950982522, "elliptic_curve", false, 16936259999292442386], [13895928991373641935, "signature", false, 3879496133662832899]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/k256-15504a13b8cc0dc0/dep-lib-k256", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}