{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"arbitrary\", \"default\", \"k256\", \"serde\", \"std\"]", "target": 17066601904469602418, "profile": 7136672544129150780, "path": 17701411052965628905, "deps": [[4552780081777059061, "alloy_primitives", false, 559759246947596331], [8844834803788054349, "alloy_eips", false, 7907979117083727447], [9689903380558560274, "serde", false, 2560059485084922610], [9890037079353029638, "alloy_serde", false, 9588819468194562351], [10806645703491011684, "thiserror", false, 17837121583403301380], [11293676373856528358, "derive_more", false, 13834774937000125114], [11709604483720470746, "op_alloy_consensus", false, 15920349395918316926], [15168588510569655375, "alloy_network_primitives", false, 17222176964516704214], [15367738274754116744, "serde_json", false, 15252519466085847446], [15466475988933116122, "alloy_rpc_types_eth", false, 4141287885098115052], [15931160604212504468, "alloy_consensus", false, 693560105979207019]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/op-alloy-rpc-types-1b2b8b9b69125ae2/dep-lib-op_alloy_rpc_types", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}