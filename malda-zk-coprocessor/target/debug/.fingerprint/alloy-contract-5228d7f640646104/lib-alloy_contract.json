{"rustc": 13226066032359371072, "features": "[\"pubsub\"]", "declared_features": "[\"pubsub\"]", "target": 5260527979114217211, "profile": 103656143193338361, "path": 16141970045687887843, "deps": [[2706460456408817945, "futures", false, 5707432882076571832], [3635609799059383815, "alloy_sol_types", false, 10270925749417062489], [4552780081777059061, "alloy_primitives", false, 559759246947596331], [6194542137026940351, "alloy_transport", false, 2008741286126587444], [6290000587278565891, "alloy_json_abi", false, 4113775928000771320], [6401697477296333510, "alloy_provider", false, 4212514201058614530], [10629569228670356391, "futures_util", false, 11880514652847822067], [10806645703491011684, "thiserror", false, 17837121583403301380], [11285052750814603390, "alloy_pubsub", false, 903583642977271948], [11671148496885528481, "alloy_dyn_abi", false, 1089067535704165614], [14325328522056656410, "alloy_network", false, 6851173837649010366], [15168588510569655375, "alloy_network_primitives", false, 17222176964516704214], [15466475988933116122, "alloy_rpc_types_eth", false, 4141287885098115052], [15931160604212504468, "alloy_consensus", false, 693560105979207019]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-contract-5228d7f640646104/dep-lib-alloy_contract", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}