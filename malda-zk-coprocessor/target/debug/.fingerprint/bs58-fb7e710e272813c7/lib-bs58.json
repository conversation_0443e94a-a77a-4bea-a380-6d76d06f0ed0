{"rustc": 13226066032359371072, "features": "[\"alloc\", \"check\", \"default\", \"sha2\", \"std\"]", "declared_features": "[\"alloc\", \"cb58\", \"check\", \"default\", \"sha2\", \"smallvec\", \"std\", \"tinyvec\"]", "target": 2243021261112611720, "profile": 15657897354478470176, "path": 1417417643415798806, "deps": [[9857275760291862238, "sha2", false, 3986058278091560241]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bs58-fb7e710e272813c7/dep-lib-bs58", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}