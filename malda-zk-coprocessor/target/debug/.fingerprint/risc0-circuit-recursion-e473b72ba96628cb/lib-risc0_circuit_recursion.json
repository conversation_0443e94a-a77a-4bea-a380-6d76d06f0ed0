{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"cuda\", \"default\", \"metal\", \"prove\", \"risc0-circuit-recursion-sys\", \"std\", \"test\"]", "target": 744416552149155838, "profile": 15657897354478470176, "path": 11457346955893701835, "deps": [[2358608249731162897, "risc0_zkp", false, 6570447398065924488], [6511429716036861196, "bytemuck", false, 18095207708388160398], [7264961447180378050, "risc0_core", false, 16839565789038798930], [8606274917505247608, "tracing", false, 12546630441409337383], [13625485746686963219, "anyhow", false, 5527867187803302802], [16097111343857175931, "build_script_build", false, 1151858849048360905]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/risc0-circuit-recursion-e473b72ba96628cb/dep-lib-risc0_circuit_recursion", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}