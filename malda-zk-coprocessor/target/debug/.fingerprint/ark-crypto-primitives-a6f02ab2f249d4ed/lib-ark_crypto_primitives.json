{"rustc": 13226066032359371072, "features": "[\"merlin\", \"snark\", \"sponge\", \"std\"]", "declared_features": "[\"ark-r1cs-std\", \"commitment\", \"crh\", \"default\", \"encryption\", \"hashbrown\", \"merkle_tree\", \"merlin\", \"parallel\", \"prf\", \"print-trace\", \"r1cs\", \"rayon\", \"signature\", \"snark\", \"sponge\", \"std\", \"tracing\"]", "target": 12232848036435804882, "profile": 15657897354478470176, "path": 5912250112640367307, "deps": [[647417929892486539, "ark_serialize", false, 3953086024402543166], [966925859616469517, "ahash", false, 14491018313961235022], [5502062331616315784, "ark_ff", false, 18328201778703591730], [8700459469608572718, "blake2", false, 12107193226193570049], [9234201994497484447, "merlin", false, 14308964740034888189], [9857275760291862238, "sha2", false, 6333067065029443388], [9889883805127379877, "ark_relations", false, 2753116387313614121], [13859769749131231458, "derivative", false, 12881640534711686890], [14614447123944235085, "ark_crypto_primitives_macros", false, 12611680972544023168], [15175849579008230926, "ark_std", false, 9492295952595228564], [16201565495264925093, "ark_snark", false, 530462081183277629], [17475753849556516473, "digest", false, 16103309049199330995], [17532637862849517517, "ark_ec", false, 9367754947677871052]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ark-crypto-primitives-a6f02ab2f249d4ed/dep-lib-ark_crypto_primitives", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}