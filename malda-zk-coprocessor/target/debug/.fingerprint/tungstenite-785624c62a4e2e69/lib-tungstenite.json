{"rustc": 13226066032359371072, "features": "[\"__rustls-tls\", \"data-encoding\", \"handshake\", \"http\", \"httparse\", \"rustls\", \"rustls-pki-types\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 5395530797274129873, "profile": 15657897354478470176, "path": 17718353472313673771, "deps": [[99287295355353247, "data_encoding", false, 11470009270974905012], [2098583196738611028, "rand", false, 5343293020115105335], [2883436298747778685, "rustls_pki_types", false, 10572488550370123930], [4359956005902820838, "utf8", false, 9876509928884779877], [5986029879202738730, "log", false, 1690976416000601508], [6163892036024256188, "httparse", false, 946295791862142201], [9010263965687315507, "http", false, 17824218619973047208], [10724389056617919257, "sha1", false, 7301026090310933041], [10806645703491011684, "thiserror", false, 17837121583403301380], [16066129441945555748, "bytes", false, 17791678820998801156], [16400140949089969347, "rustls", false, 11276471013002673777]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tungstenite-785624c62a4e2e69/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}