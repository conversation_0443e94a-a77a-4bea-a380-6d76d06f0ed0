{"rustc": 13226066032359371072, "features": "[\"default\", \"k256\", \"serde\", \"std\"]", "declared_features": "[\"arbitrary\", \"default\", \"k256\", \"kzg\", \"secp256k1\", \"serde\", \"serde-bincode-compat\", \"serde_with\", \"std\"]", "target": 2929293942009581624, "profile": 103656143193338361, "path": 17265317210944172886, "deps": [[3434989764622224963, "k256", false, 1108302903643786641], [3722963349756955755, "once_cell", false, 12396378922497620399], [4552780081777059061, "alloy_primitives", false, 4141762026781029709], [5597578105680351444, "alloy_rlp", false, 873648323400137026], [7113539788457516621, "alloy_trie", false, 5076566261811867880], [8844834803788054349, "alloy_eips", false, 16353252329032128135], [9689903380558560274, "serde", false, 6295511631161797798], [9890037079353029638, "alloy_serde", false, 6250537875913558812], [10806645703491011684, "thiserror", false, 13177199886962927542], [11293676373856528358, "derive_more", false, 13580242526917472243], [12170264697963848012, "either", false, 3316987715512718837], [18125022703902813197, "auto_impl", false, 7779807868399602672]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-consensus-2d8813b5a0b58263/dep-lib-alloy_consensus", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}