{"rustc": 13226066032359371072, "features": "[\"arithmetic\", \"default\", \"digest\", \"ecdsa\", \"ecdsa-core\", \"pkcs8\", \"schnorr\", \"sha2\", \"sha256\", \"std\"]", "declared_features": "[\"arithmetic\", \"bits\", \"default\", \"digest\", \"ecdh\", \"ecdsa\", \"ecdsa-core\", \"expose-field\", \"hash2curve\", \"hex-literal\", \"jwk\", \"keccak256\", \"pem\", \"pkcs8\", \"schnorr\", \"serde\", \"serdect\", \"sha2\", \"sha256\", \"sha3\", \"std\", \"test-vectors\"]", "target": 606099465376125212, "profile": 15657897354478470176, "path": 5417935772296554762, "deps": [[2828590642173593838, "cfg_if", false, 1903128207448990402], [4979874718420827504, "ecdsa_core", false, 16878018318825426924], [8003439051071108387, "elliptic_curve", false, 15532958648155280472], [9857275760291862238, "sha2", false, 3986058278091560241]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/k256-beb07cd31cae7446/dep-lib-k256", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}