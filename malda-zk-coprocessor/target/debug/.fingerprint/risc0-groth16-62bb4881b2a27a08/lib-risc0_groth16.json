{"rustc": 13226066032359371072, "features": "[\"std\"]", "declared_features": "[\"default\", \"prove\", \"std\", \"unstable\"]", "target": 10587424292316905330, "profile": 2241668132362809309, "path": 9929349744196877603, "deps": [[530211389790465181, "hex", false, 10292843913979569116], [647417929892486539, "ark_serialize", false, 7082038868372887304], [2358608249731162897, "risc0_zkp", false, 16377063629553783524], [6151811949586245694, "ark_bn254", false, 13377958202017431209], [6511429716036861196, "bytemuck", false, 5970047994285344772], [9246984816025833931, "ark_groth16", false, 15111161263865014302], [9689903380558560274, "serde", false, 13902469085514894929], [11115194146618580017, "stability", false, 2747746342742252058], [11457159213880670435, "risc0_binfmt", false, 12144665683121825415], [12528732512569713347, "num_bigint", false, 12151259233769890211], [13625485746686963219, "anyhow", false, 9879049321265876028], [17532637862849517517, "ark_ec", false, 14550774034805136563]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/risc0-groth16-62bb4881b2a27a08/dep-lib-risc0_groth16", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}