{"rustc": 13226066032359371072, "features": "[\"<PERSON><PERSON>\", \"Copy\", \"Debug\", \"Default\", \"Deref\", \"DerefMut\", \"Eq\", \"Hash\", \"Into\", \"Ord\", \"PartialEq\", \"PartialOrd\", \"default\"]", "declared_features": "[\"<PERSON><PERSON>\", \"Copy\", \"Debug\", \"Default\", \"Deref\", \"DerefMut\", \"Eq\", \"Hash\", \"Into\", \"Ord\", \"PartialEq\", \"PartialOrd\", \"default\", \"full\"]", "target": 9536858342250534546, "profile": 2225463790103693989, "path": 13871538158524368924, "deps": [[3060637413840920116, "proc_macro2", false, 11978631202661594756], [7890276103475655350, "enum_ordinalize", false, 10309546177296575008], [10640660562325816595, "syn", false, 15938410596040983936], [17990358020177143287, "quote", false, 4968544993622710939]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/educe-0694c9b73f95d5c1/dep-lib-educe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}