{"rustc": 13226066032359371072, "features": "[\"abigen\", \"ethers-contract-abigen\", \"ethers-contract-derive\", \"ethers-providers\", \"providers\", \"rustls\"]", "declared_features": "[\"abigen\", \"abigen-offline\", \"abigen-online\", \"celo\", \"default\", \"eip712\", \"ethers-contract-abigen\", \"ethers-contract-derive\", \"ethers-providers\", \"legacy\", \"openssl\", \"optimism\", \"providers\", \"rustls\"]", "target": 7173054194013103692, "profile": 15657897354478470176, "path": 11290977974409434104, "deps": [[2141440161325128830, "ethers_contract_abigen", false, 17927913714542075904], [3722963349756955755, "once_cell", false, 12396378922497620399], [5030409040180886167, "ethers_core", false, 12516761227293124983], [6264115378959545688, "pin_project", false, 2191553817959923055], [8008191657135824715, "thiserror", false, 7776962857085638134], [8524202948771681628, "ethers_contract_derive", false, 5875012355725932795], [9689903380558560274, "serde", false, 2560059485084922610], [10629569228670356391, "futures_util", false, 11880514652847822067], [13003293521383684806, "hex", false, 3398834851042638974], [14417566112632113257, "ethers_providers", false, 5997969808708307643], [15367738274754116744, "serde_json", false, 15252519466085847446]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ethers-contract-da9894bcd78820e3/dep-lib-ethers_contract", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}