{"rustc": 13226066032359371072, "features": "[\"alloc\", \"arithmetic\", \"der\", \"digest\", \"hazmat\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"signing\", \"spki\", \"std\", \"verifying\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"default\", \"der\", \"dev\", \"digest\", \"hazmat\", \"pem\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"sha2\", \"signing\", \"spki\", \"std\", \"verifying\"]", "target": 5012119522651993362, "profile": 15657897354478470176, "path": 2858049807385838218, "deps": [[4234225094004207019, "rfc6979", false, 12226509125212798254], [5844362839343846847, "serdect", false, 762528698932746335], [10149501514950982522, "elliptic_curve", false, 9578139742083461532], [10800937535932116261, "der", false, 4668809469406379964], [11285023886693207100, "spki", false, 14904350976708734541], [13895928991373641935, "signature", false, 9829658913023155033], [17475753849556516473, "digest", false, 16103309049199330995]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ecdsa-0f65b6318a5ea348/dep-lib-ecdsa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}