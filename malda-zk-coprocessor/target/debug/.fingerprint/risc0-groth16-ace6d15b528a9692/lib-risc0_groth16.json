{"rustc": 13226066032359371072, "features": "[\"std\"]", "declared_features": "[\"default\", \"prove\", \"std\", \"unstable\"]", "target": 10587424292316905330, "profile": 15657897354478470176, "path": 9929349744196877603, "deps": [[530211389790465181, "hex", false, 3379478178411117322], [647417929892486539, "ark_serialize", false, 3953086024402543166], [2358608249731162897, "risc0_zkp", false, 6570447398065924488], [6151811949586245694, "ark_bn254", false, 11965139458425219971], [6511429716036861196, "bytemuck", false, 18095207708388160398], [9246984816025833931, "ark_groth16", false, 1385003098758006757], [9689903380558560274, "serde", false, 6295511631161797798], [11115194146618580017, "stability", false, 2747746342742252058], [11457159213880670435, "risc0_binfmt", false, 18318630182677350647], [12528732512569713347, "num_bigint", false, 634536904297996218], [13625485746686963219, "anyhow", false, 5527867187803302802], [17532637862849517517, "ark_ec", false, 9367754947677871052]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/risc0-groth16-ace6d15b528a9692/dep-lib-risc0_groth16", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}