{"rustc": 13226066032359371072, "features": "[\"basic-cookies\", \"cookies\", \"default\"]", "declared_features": "[\"basic-cookies\", \"clap\", \"color\", \"colored\", \"cookies\", \"default\", \"env_logger\", \"isahc\", \"remote\", \"serde_yaml\", \"standalone\"]", "target": 608559532794175849, "profile": 15657897354478470176, "path": 6494033261159869397, "deps": [[15171501230327402, "async_std", false, 9793460519390226254], [915123552320100963, "similar", false, 12724971584868336746], [3150220818285335163, "url", false, 14930828032240483960], [3580020307953720212, "async_object_pool", false, 15838174242732627639], [4468123440088164316, "crossbeam_utils", false, 3298676960474222745], [5026306216841577341, "<PERSON><PERSON><PERSON><PERSON>", false, 7423758445432627386], [5849048857304117613, "assert_json_diff", false, 6262861961656910552], [5986029879202738730, "log", false, 1690976416000601508], [7414427314941361239, "hyper", false, 10917647327871319383], [9451456094439810778, "regex", false, 5343794302709782301], [9538054652646069845, "tokio", false, 583227580819415231], [9689903380558560274, "serde", false, 2560059485084922610], [10629569228670356391, "futures_util", false, 11880514652847822067], [11946729385090170470, "async_trait", false, 11822780007081638092], [12613081344515805984, "serde_regex", false, 417715774594455036], [14299496324343720937, "form_urlencoded", false, 11451432830400987484], [14830292086846978678, "basic_cookies", false, 14238450158209385133], [15367738274754116744, "serde_json", false, 15252519466085847446], [17917672826516349275, "lazy_static", false, 16865043140197432664], [18066890886671768183, "base64", false, 7170181336729517116]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/httpmock-60c5729082f93df4/dep-lib-httpmock", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}