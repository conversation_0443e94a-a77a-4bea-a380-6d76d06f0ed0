{"rustc": 13226066032359371072, "features": "[\"std\"]", "declared_features": "[\"default\", \"prove\", \"std\", \"unstable\"]", "target": 10587424292316905330, "profile": 15657897354478470176, "path": 9929349744196877603, "deps": [[530211389790465181, "hex", false, 9639909450964605597], [647417929892486539, "ark_serialize", false, 840149142461284268], [2358608249731162897, "risc0_zkp", false, 8277126296413452934], [6151811949586245694, "ark_bn254", false, 6732320419032315367], [6511429716036861196, "bytemuck", false, 9910068359880798062], [9246984816025833931, "ark_groth16", false, 3253207164129925583], [9689903380558560274, "serde", false, 2560059485084922610], [11115194146618580017, "stability", false, 14936267954155763698], [11457159213880670435, "risc0_binfmt", false, 17161124386537394721], [12528732512569713347, "num_bigint", false, 18123728405935323858], [13625485746686963219, "anyhow", false, 5527867187803302802], [17532637862849517517, "ark_ec", false, 12054792134977161439]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/risc0-groth16-5f4f6ab3ed09c81f/dep-lib-risc0_groth16", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}