{"rustc": 13226066032359371072, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 3965174974797606104, "profile": 15657897354478470176, "path": 5545211221382950454, "deps": [[99287295355353247, "data_encoding", false, 11470009270974905012], [3712811570531045576, "byteorder", false, 6894596790329543132], [4359956005902820838, "utf8", false, 9876509928884779877], [5986029879202738730, "log", false, 1690976416000601508], [6163892036024256188, "httparse", false, 946295791862142201], [8008191657135824715, "thiserror", false, 7776962857085638134], [9010263965687315507, "http", false, 17824218619973047208], [10724389056617919257, "sha1", false, 7301026090310933041], [13208667028893622512, "rand", false, 13395923389046576688], [16066129441945555748, "bytes", false, 17791678820998801156], [16785601910559813697, "native_tls_crate", false, 5497874130712142210]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tungstenite-a9b10f7934727891/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}