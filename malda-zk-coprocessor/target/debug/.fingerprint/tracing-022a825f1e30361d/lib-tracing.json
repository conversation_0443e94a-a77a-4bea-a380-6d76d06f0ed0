{"rustc": 13226066032359371072, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 11202463608144111571, "path": 4881087286698293964, "deps": [[1906322745568073236, "pin_project_lite", false, 6348330080065982410], [3424551429995674438, "tracing_core", false, 10820017051342959744], [5986029879202738730, "log", false, 1181718507905414799], [15574202673389706213, "tracing_attributes", false, 11297205105478166165]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-022a825f1e30361d/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}