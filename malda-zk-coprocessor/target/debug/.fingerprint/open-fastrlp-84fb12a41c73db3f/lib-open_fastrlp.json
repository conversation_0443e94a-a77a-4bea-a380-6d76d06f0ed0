{"rustc": 13226066032359371072, "features": "[\"alloc\", \"derive\", \"ethereum-types\", \"rlp-derive\", \"std\"]", "declared_features": "[\"alloc\", \"derive\", \"ethereum-types\", \"ethnum\", \"rlp-derive\", \"std\"]", "target": 13017501929983952138, "profile": 15657897354478470176, "path": 12670416879934119149, "deps": [[4659183844312964618, "ethereum_types", false, 17293316667441886358], [9053024541501512622, "rlp_derive", false, 3549439605833192580], [13847662864258534762, "arrayvec", false, 18251318086638771388], [16066129441945555748, "bytes", false, 1451183053652840963], [18125022703902813197, "auto_impl", false, 13785375070100931947]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/open-fastrlp-84fb12a41c73db3f/dep-lib-open_fastrlp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}