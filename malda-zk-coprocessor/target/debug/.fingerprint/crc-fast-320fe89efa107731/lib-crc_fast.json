{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"alloc\", \"optimize_crc32_auto\", \"optimize_crc32_avx512_v4s3x3\", \"optimize_crc32_avx512_vpclmulqdq_v3x2\", \"optimize_crc32_neon_blended\", \"optimize_crc32_neon_eor3_v9s3x2e_s3\", \"optimize_crc32_neon_v12e_v1\", \"optimize_crc32_neon_v3s4x2e_v2\", \"optimize_crc32_sse_v4s3x3\", \"vpclmulqdq\"]", "target": 12225653818132177521, "profile": 15657897354478470176, "path": 9164891350384579961, "deps": [[1162433738665300155, "crc", false, 13261163145002083378], [2098583196738611028, "rand", false, 5343293020115105335], [5330658427305787935, "libc", false, 13924901093407034193], [9451456094439810778, "regex", false, 5343794302709782301], [17475753849556516473, "digest", false, 11922288272037430477]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/crc-fast-320fe89efa107731/dep-lib-crc_fast", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}