{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"arbitrary\", \"default\", \"k256\", \"serde\", \"std\"]", "target": 17066601904469602418, "profile": 12475796322115683109, "path": 17701411052965628905, "deps": [[4552780081777059061, "alloy_primitives", false, 1708916969974984938], [8844834803788054349, "alloy_eips", false, 7134968047005548121], [9689903380558560274, "serde", false, 13902469085514894929], [9890037079353029638, "alloy_serde", false, 5934534825539963960], [10806645703491011684, "thiserror", false, 16435724550757905811], [11293676373856528358, "derive_more", false, 6031128645935574517], [11709604483720470746, "op_alloy_consensus", false, 3425411148843133054], [15168588510569655375, "alloy_network_primitives", false, 4669398498492056606], [15367738274754116744, "serde_json", false, 2579467649851281210], [15466475988933116122, "alloy_rpc_types_eth", false, 1804962683940463234], [15931160604212504468, "alloy_consensus", false, 6025113663526452639]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/op-alloy-rpc-types-a60c50f1f261b411/dep-lib-op_alloy_rpc_types", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}