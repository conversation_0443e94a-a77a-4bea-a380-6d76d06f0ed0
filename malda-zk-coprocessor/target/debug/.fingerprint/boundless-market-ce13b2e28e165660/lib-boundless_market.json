{"rustc": 13226066032359371072, "features": "[\"default\"]", "declared_features": "[\"default\", \"test-utils\"]", "target": 4246223855618440096, "profile": 15657897354478470176, "path": 15472354779284835382, "deps": [[384373354905502547, "risc0_zkvm", false, 4576743152978539914], [530211389790465181, "hex", false, 9639909450964605597], [1188017320647144970, "async_stream", false, 11028403058918822086], [1441306149310335789, "tempfile", false, 5845473505042761149], [2098583196738611028, "rand", false, 5343293020115105335], [2706460456408817945, "futures", false, 5707432882076571832], [3150220818285335163, "url", false, 14930828032240483960], [3635609799059383815, "alloy_sol_types", false, 10270925749417062489], [4552780081777059061, "alloy_primitives", false, 559759246947596331], [5270560984710784109, "siwe", false, 3826522115837458621], [5404164422603516794, "risc0_ethereum_contracts", false, 1268115004342934814], [6219554740863759696, "derive_builder", false, 8213932246355064318], [6328167575312831016, "tokio_tungstenite", false, 8412259005468986252], [6511429716036861196, "bytemuck", false, 9910068359880798062], [6543923330077242379, "alloy", false, 10923823421164667770], [7238338081869314361, "build_script_build", false, 3204979368612973452], [7763653763800200228, "rmp_serde", false, 7404162641091467591], [8218178811151724123, "reqwest", false, 6940494522176607400], [8606274917505247608, "tracing", false, 6942084126805219901], [9538054652646069845, "tokio", false, 583227580819415231], [9689903380558560274, "serde", false, 2560059485084922610], [9857275760291862238, "sha2", false, 3986058278091560241], [9897246384292347999, "chrono", false, 5052018083537030113], [10058419304232285389, "aws_sdk_s3", false, 3016764246881625165], [10283607363747246536, "alloy_chains", false, 9775480860450423034], [10448019748683118955, "dashmap", false, 11395478818636890757], [10629569228670356391, "futures_util", false, 11880514652847822067], [10806645703491011684, "thiserror", false, 17837121583403301380], [11946729385090170470, "async_trait", false, 11822780007081638092], [12409575957772518135, "time", false, 3921441521621016959], [13625485746686963219, "anyhow", false, 5527867187803302802], [15367738274754116744, "serde_json", false, 15252519466085847446], [15895381428815746701, "u<PERSON><PERSON>a", false, 13901084790585175190], [16230660778393187092, "tracing_subscriber", false, 1659518823055846522], [17233500904167941097, "risc0_aggregation", false, 5211669174722708589], [17433017841942338824, "clap", false, 7365018969571385643], [17668234333162111562, "httpmock", false, 7073290525642372163]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/boundless-market-ce13b2e28e165660/dep-lib-boundless_market", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}