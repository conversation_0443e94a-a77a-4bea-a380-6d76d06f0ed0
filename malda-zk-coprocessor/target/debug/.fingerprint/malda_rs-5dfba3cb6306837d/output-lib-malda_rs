{"$message_type":"diagnostic","message":"unused import: `sol_types::SolValue`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"malda_rs/src/viewcalls.rs","byte_start":3087,"byte_end":3106,"line_start":86,"line_end":86,"column_start":47,"column_end":66,"is_primary":true,"text":[{"text":"use alloy::{signers::local::PrivateKeySigner, sol_types::SolValue};","highlight_start":47,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"malda_rs/src/viewcalls.rs","byte_start":3085,"byte_end":3106,"line_start":86,"line_end":86,"column_start":45,"column_end":66,"is_primary":true,"text":[{"text":"use alloy::{signers::local::PrivateKeySigner, sol_types::SolValue};","highlight_start":45,"highlight_end":66}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"malda_rs/src/viewcalls.rs","byte_start":3052,"byte_end":3053,"line_start":86,"line_end":86,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use alloy::{signers::local::PrivateKeySigner, sol_types::SolValue};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"malda_rs/src/viewcalls.rs","byte_start":3106,"byte_end":3107,"line_start":86,"line_end":86,"column_start":66,"column_end":67,"is_primary":true,"text":[{"text":"use alloy::{signers::local::PrivateKeySigner, sol_types::SolValue};","highlight_start":66,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `sol_types::SolValue`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mmalda_rs/src/viewcalls.rs:86:47\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse alloy::{signers::local::PrivateKeySigner, sol_types::SolValue};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"1 warning emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 1 warning emitted\u001b[0m\n\n"}
