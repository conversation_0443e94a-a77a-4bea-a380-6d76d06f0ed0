{"rustc": 13226066032359371072, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 6355579909791343455, "path": 14941789077292118028, "deps": [[1009387600818341822, "matchers", false, 7717362470241657512], [1017461770342116999, "sharded_slab", false, 11766243137604936423], [1359731229228270592, "thread_local", false, 6584138562872937250], [3424551429995674438, "tracing_core", false, 4232886703490372862], [3666196340704888985, "smallvec", false, 3090956824274358094], [3722963349756955755, "once_cell", false, 12396378922497620399], [8606274917505247608, "tracing", false, 6942084126805219901], [8614575489689151157, "nu_ansi_term", false, 16847815518721226717], [9451456094439810778, "regex", false, 5343794302709782301], [10806489435541507125, "tracing_log", false, 15555480425634939106]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-9584b6edb218231c/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}