{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"k256\"]", "target": 14063547271430870162, "profile": 1455982124524106486, "path": 4372659475511370489, "deps": [[702787943357950849, "futures_utils_wasm", false, 126239461381104597], [2959752630505841847, "alloy_json_rpc", false, 13407339193750199709], [3635609799059383815, "alloy_sol_types", false, 2320068993150984763], [4234611482138378758, "alloy_consensus_any", false, 12665567558108608671], [4552780081777059061, "alloy_primitives", false, 1708916969974984938], [7168537401094488756, "alloy_signer", false, 7632765121992699631], [8844834803788054349, "alloy_eips", false, 7134968047005548121], [9216901827022316173, "alloy_rpc_types_any", false, 7868570775628777935], [9689903380558560274, "serde", false, 13902469085514894929], [9890037079353029638, "alloy_serde", false, 5934534825539963960], [10806645703491011684, "thiserror", false, 16435724550757905811], [11293676373856528358, "derive_more", false, 6031128645935574517], [11946729385090170470, "async_trait", false, 5529371514338439685], [15168588510569655375, "alloy_network_primitives", false, 4669398498492056606], [15367738274754116744, "serde_json", false, 2579467649851281210], [15466475988933116122, "alloy_rpc_types_eth", false, 1804962683940463234], [15931160604212504468, "alloy_consensus", false, 6025113663526452639], [18125022703902813197, "auto_impl", false, 7779807868399602672]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-network-7eae233daff4c4c9/dep-lib-alloy_network", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}