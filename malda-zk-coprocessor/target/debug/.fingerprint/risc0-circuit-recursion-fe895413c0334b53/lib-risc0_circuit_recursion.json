{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"cuda\", \"default\", \"metal\", \"prove\", \"risc0-circuit-recursion-sys\", \"std\", \"test\"]", "target": 744416552149155838, "profile": 15657897354478470176, "path": 11457346955893701835, "deps": [[2358608249731162897, "risc0_zkp", false, 6490320026995620176], [6511429716036861196, "bytemuck", false, 9910068359880798062], [7264961447180378050, "risc0_core", false, 13069037472218983112], [8606274917505247608, "tracing", false, 4834628962980689165], [13625485746686963219, "anyhow", false, 5527867187803302802], [16097111343857175931, "build_script_build", false, 2191880890138709415]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/risc0-circuit-recursion-fe895413c0334b53/dep-lib-risc0_circuit_recursion", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}