{"rustc": 13226066032359371072, "features": "[\"alloc\", \"arithmetic\", \"default\", \"digest\", \"ecdsa\", \"ecdsa-core\", \"expose-field\", \"once_cell\", \"pkcs8\", \"precomputed-tables\", \"schnorr\", \"serde\", \"serdect\", \"sha2\", \"sha256\", \"signature\", \"std\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"bits\", \"critical-section\", \"default\", \"digest\", \"ecdh\", \"ecdsa\", \"ecdsa-core\", \"expose-field\", \"hash2curve\", \"hex-literal\", \"jwk\", \"once_cell\", \"pem\", \"pkcs8\", \"precomputed-tables\", \"schnorr\", \"serde\", \"serdect\", \"sha2\", \"sha256\", \"signature\", \"std\", \"test-vectors\"]", "target": 2074457694779954094, "profile": 15657897354478470176, "path": 11286597672375728731, "deps": [[2348975382319678783, "ecdsa_core", false, 14382105290869134241], [2828590642173593838, "cfg_if", false, 1903128207448990402], [3722963349756955755, "once_cell", false, 12396378922497620399], [5844362839343846847, "serdect", false, 762528698932746335], [9857275760291862238, "sha2", false, 6333067065029443388], [10149501514950982522, "elliptic_curve", false, 9578139742083461532], [13895928991373641935, "signature", false, 9829658913023155033]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/k256-07ab46c505b3be7c/dep-lib-k256", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}