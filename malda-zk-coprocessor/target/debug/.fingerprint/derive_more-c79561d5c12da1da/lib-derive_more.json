{"rustc": 13226066032359371072, "features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 7165309211519594838, "profile": 1218695365660037764, "path": 17552253428284531037, "deps": [[15774985133158646067, "derive_more_impl", false, 7353146185002450073]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-c79561d5c12da1da/dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}