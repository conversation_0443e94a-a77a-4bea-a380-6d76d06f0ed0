{"rustc": 13226066032359371072, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"crc\", \"default\", \"defmt\", \"embedded-io\", \"embedded-io-04\", \"embedded-io-06\", \"experimental-derive\", \"heapless\", \"heapless-cas\", \"paste\", \"postcard-derive\", \"use-crc\", \"use-defmt\", \"use-std\"]", "target": 7941872121969890562, "profile": 15657897354478470176, "path": 17164985629944136445, "deps": [[9689903380558560274, "serde", false, 6295511631161797798], [13902618262990712667, "cobs", false, 10387667201536927661]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/postcard-64b817da38589884/dep-lib-postcard", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}