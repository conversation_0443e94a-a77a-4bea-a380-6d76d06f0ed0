{"rustc": 13226066032359371072, "features": "[\"alloc\", \"arithmetic\", \"digest\", \"ff\", \"group\", \"hazmat\", \"pkcs8\", \"sec1\", \"serde\", \"std\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"bits\", \"default\", \"dev\", \"digest\", \"ecdh\", \"ff\", \"group\", \"hash2curve\", \"hazmat\", \"jwk\", \"pem\", \"pkcs8\", \"sec1\", \"serde\", \"std\", \"voprf\"]", "target": 3243834021826523897, "profile": 2241668132362809309, "path": 1999902554121838114, "deps": [[5218994449591892524, "sec1", false, 3098552335873764304], [5844362839343846847, "serdect", false, 18029600520281923744], [6528079939221783635, "zeroize", false, 7045706449730503893], [10520923840501062997, "generic_array", false, 16993506146240857271], [11558297082666387394, "crypto_bigint", false, 16046809607085012568], [13163366046229301192, "group", false, 1680049693383025527], [16464744132169923781, "ff", false, 6318511807135816681], [16530257588157702925, "base16ct", false, 9676477823107326414], [17003143334332120809, "subtle", false, 1785308241731029061], [17064813216363465056, "pkcs8", false, 15954980706129037112], [17475753849556516473, "digest", false, 140149734445324485], [18130209639506977569, "rand_core", false, 2294235732715420626]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/elliptic-curve-dc10f312096ff5c5/dep-lib-elliptic_curve", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}