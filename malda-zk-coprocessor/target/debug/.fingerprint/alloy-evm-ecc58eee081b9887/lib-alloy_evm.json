{"rustc": 13226066032359371072, "features": "[\"default\", \"op\", \"op-alloy-consensus\", \"op-revm\", \"std\"]", "declared_features": "[\"default\", \"op\", \"op-alloy-consensus\", \"op-revm\", \"secp256k1\", \"std\"]", "target": 5176978098157706261, "profile": 6436667884522905088, "path": 8466379190618631504, "deps": [[156817798628101091, "revm", false, 9477604197044408454], [3635609799059383815, "alloy_sol_types", false, 9351913518488021779], [4552780081777059061, "alloy_primitives", false, 4141762026781029709], [6076877872687043960, "op_revm", false, 6215017360559259278], [8844834803788054349, "alloy_eips", false, 16353252329032128135], [10806645703491011684, "thiserror", false, 13177199886962927542], [11293676373856528358, "derive_more", false, 13580242526917472243], [11709604483720470746, "op_alloy_consensus", false, 14727588637997250126], [15931160604212504468, "alloy_consensus", false, 10798358941298809571], [16453484564107164156, "alloy_hardforks", false, 12236995213098129254], [18125022703902813197, "auto_impl", false, 7779807868399602672]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-evm-ecc58eee081b9887/dep-lib-alloy_evm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}