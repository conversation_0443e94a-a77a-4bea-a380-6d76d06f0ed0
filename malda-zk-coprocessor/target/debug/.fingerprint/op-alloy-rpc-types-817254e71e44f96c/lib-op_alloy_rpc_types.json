{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"arbitrary\", \"default\", \"k256\", \"serde\", \"std\"]", "target": 17066601904469602418, "profile": 7136672544129150780, "path": 17701411052965628905, "deps": [[4552780081777059061, "alloy_primitives", false, 4141762026781029709], [8844834803788054349, "alloy_eips", false, 16353252329032128135], [9689903380558560274, "serde", false, 6295511631161797798], [9890037079353029638, "alloy_serde", false, 6250537875913558812], [10806645703491011684, "thiserror", false, 13177199886962927542], [11293676373856528358, "derive_more", false, 13580242526917472243], [11709604483720470746, "op_alloy_consensus", false, 14727588637997250126], [15168588510569655375, "alloy_network_primitives", false, 10553853110562602677], [15367738274754116744, "serde_json", false, 15970461120745706765], [15466475988933116122, "alloy_rpc_types_eth", false, 3291276077316372451], [15931160604212504468, "alloy_consensus", false, 10798358941298809571]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/op-alloy-rpc-types-817254e71e44f96c/dep-lib-op_alloy_rpc_types", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}