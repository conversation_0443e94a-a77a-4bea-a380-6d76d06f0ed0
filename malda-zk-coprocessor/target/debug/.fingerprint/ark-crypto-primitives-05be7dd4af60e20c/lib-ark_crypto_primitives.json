{"rustc": 13226066032359371072, "features": "[\"merlin\", \"snark\", \"sponge\", \"std\"]", "declared_features": "[\"ark-r1cs-std\", \"commitment\", \"crh\", \"default\", \"encryption\", \"hashbrown\", \"merkle_tree\", \"merlin\", \"parallel\", \"prf\", \"print-trace\", \"r1cs\", \"rayon\", \"signature\", \"snark\", \"sponge\", \"std\", \"tracing\"]", "target": 12232848036435804882, "profile": 2241668132362809309, "path": 5912250112640367307, "deps": [[647417929892486539, "ark_serialize", false, 7082038868372887304], [966925859616469517, "ahash", false, 3566357431508620348], [5502062331616315784, "ark_ff", false, 15569003004039897809], [8700459469608572718, "blake2", false, 2508801306179264218], [9234201994497484447, "merlin", false, 17663457394180303658], [9857275760291862238, "sha2", false, 4002209552109299503], [9889883805127379877, "ark_relations", false, 8593390945879956550], [13859769749131231458, "derivative", false, 12881640534711686890], [14614447123944235085, "ark_crypto_primitives_macros", false, 12611680972544023168], [15175849579008230926, "ark_std", false, 9232832439194781425], [16201565495264925093, "ark_snark", false, 12674879251277746847], [17475753849556516473, "digest", false, 140149734445324485], [17532637862849517517, "ark_ec", false, 14550774034805136563]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ark-crypto-primitives-05be7dd4af60e20c/dep-lib-ark_crypto_primitives", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}