{"rustc": 13226066032359371072, "features": "[\"__rustls\", \"__tls\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 15657897354478470176, "path": 2028245155604586586, "deps": [[40386456601120721, "percent_encoding", false, 13100953575333840213], [95042085696191081, "ipnet", false, 12007083731717953923], [264090853244900308, "sync_wrapper", false, 5758697284906206414], [784494742817713399, "tower_service", false, 12193628328957816398], [1044435446100926395, "hyper_rustls", false, 4012519071680010994], [1906322745568073236, "pin_project_lite", false, 3192898420806307456], [3150220818285335163, "url", false, 14930828032240483960], [3722963349756955755, "once_cell", false, 12396378922497620399], [4405182208873388884, "http", false, 7754831301747032745], [5986029879202738730, "log", false, 1690976416000601508], [7414427314941361239, "hyper", false, 10917647327871319383], [7620660491849607393, "futures_core", false, 1559213636597315443], [8915503303801890683, "http_body", false, 15881017158979046062], [9538054652646069845, "tokio", false, 583227580819415231], [9689903380558560274, "serde", false, 2560059485084922610], [10229185211513642314, "mime", false, 6699806921528225474], [10629569228670356391, "futures_util", false, 11880514652847822067], [11295624341523567602, "rustls", false, 9315527580932235824], [13809605890706463735, "h2", false, 10315831764801658213], [14564311161534545801, "encoding_rs", false, 5243805418299428991], [15367738274754116744, "serde_json", false, 15252519466085847446], [16066129441945555748, "bytes", false, 17791678820998801156], [16311359161338405624, "rustls_pemfile", false, 14795650749220760176], [16542808166767769916, "serde_urlencoded", false, 1270566881896550072], [16622232390123975175, "tokio_rustls", false, 6953980270362584123], [17652733826348741533, "webpki_roots", false, 7293491152479267001], [18066890886671768183, "base64", false, 7170181336729517116]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-8ed9b55893ebc359/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}