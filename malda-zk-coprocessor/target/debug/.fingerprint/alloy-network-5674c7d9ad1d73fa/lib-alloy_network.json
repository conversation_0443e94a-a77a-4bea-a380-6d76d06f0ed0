{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"k256\"]", "target": 14063547271430870162, "profile": 103656143193338361, "path": 4372659475511370489, "deps": [[702787943357950849, "futures_utils_wasm", false, 5892083594587345041], [2959752630505841847, "alloy_json_rpc", false, 8124739160713643339], [3635609799059383815, "alloy_sol_types", false, 9351913518488021779], [4234611482138378758, "alloy_consensus_any", false, 2896987748952425181], [4552780081777059061, "alloy_primitives", false, 4141762026781029709], [7168537401094488756, "alloy_signer", false, 2341186241931035307], [8844834803788054349, "alloy_eips", false, 16353252329032128135], [9216901827022316173, "alloy_rpc_types_any", false, 13930781101351985726], [9689903380558560274, "serde", false, 6295511631161797798], [9890037079353029638, "alloy_serde", false, 6250537875913558812], [10806645703491011684, "thiserror", false, 13177199886962927542], [11293676373856528358, "derive_more", false, 13580242526917472243], [11946729385090170470, "async_trait", false, 5529371514338439685], [15168588510569655375, "alloy_network_primitives", false, 10553853110562602677], [15367738274754116744, "serde_json", false, 15970461120745706765], [15466475988933116122, "alloy_rpc_types_eth", false, 3291276077316372451], [15931160604212504468, "alloy_consensus", false, 10798358941298809571], [18125022703902813197, "auto_impl", false, 7779807868399602672]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-network-5674c7d9ad1d73fa/dep-lib-alloy_network", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}