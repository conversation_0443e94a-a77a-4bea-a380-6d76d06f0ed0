{"rustc": 13226066032359371072, "features": "[\"alloc\", \"arithmetic\", \"digest\", \"ff\", \"group\", \"hazmat\", \"pkcs8\", \"sec1\", \"serde\", \"std\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"bits\", \"default\", \"dev\", \"digest\", \"ecdh\", \"ff\", \"group\", \"hash2curve\", \"hazmat\", \"jwk\", \"pem\", \"pkcs8\", \"sec1\", \"serde\", \"std\", \"voprf\"]", "target": 3243834021826523897, "profile": 15657897354478470176, "path": 1999902554121838114, "deps": [[5218994449591892524, "sec1", false, 11285335219049184214], [5844362839343846847, "serdect", false, 762528698932746335], [6528079939221783635, "zeroize", false, 15705616763576444172], [10520923840501062997, "generic_array", false, 16652478495042139050], [11558297082666387394, "crypto_bigint", false, 11035754356097045629], [13163366046229301192, "group", false, 6438179722876469855], [16464744132169923781, "ff", false, 3854120432095490581], [16530257588157702925, "base16ct", false, 3198183149828544555], [17003143334332120809, "subtle", false, 16084265788345507804], [17064813216363465056, "pkcs8", false, 15363230611068052846], [17475753849556516473, "digest", false, 16103309049199330995], [18130209639506977569, "rand_core", false, 7904627277969005119]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/elliptic-curve-335bad58fd2421ee/dep-lib-elliptic_curve", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}