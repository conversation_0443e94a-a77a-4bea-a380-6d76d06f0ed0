{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"default\", \"prove\", \"std\", \"unstable\"]", "target": 10587424292316905330, "profile": 2225463790103693989, "path": 9929349744196877603, "deps": [[530211389790465181, "hex", false, 13143765463126439057], [647417929892486539, "ark_serialize", false, 16633827611021036118], [2358608249731162897, "risc0_zkp", false, 6490320026995620176], [6151811949586245694, "ark_bn254", false, 11899409919458176397], [6511429716036861196, "bytemuck", false, 9910068359880798062], [9246984816025833931, "ark_groth16", false, 14766814395573776345], [9689903380558560274, "serde", false, 9954545152113246272], [11115194146618580017, "stability", false, 14936267954155763698], [11457159213880670435, "risc0_binfmt", false, 15480883549032226095], [12528732512569713347, "num_bigint", false, 4040694819926368784], [13625485746686963219, "anyhow", false, 5527867187803302802], [17532637862849517517, "ark_ec", false, 2502811495959916507]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/risc0-groth16-89c50f1d0662c5e5/dep-lib-risc0_groth16", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}