{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"hardcoded-credentials\", \"test-util\"]", "target": 9398142247608084005, "profile": 15657897354478470176, "path": 13769601320939088997, "deps": [[597652819505411233, "aws_smithy_async", false, 5289235676388137859], [6528079939221783635, "zeroize", false, 4534150598234199993], [10535238618256657085, "aws_smithy_types", false, 18027050269860777449], [16503430488003824554, "aws_smithy_runtime_api", false, 11181949005332787390]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/aws-credential-types-77cd84c4e10dbde0/dep-lib-aws_credential_types", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}