{"rustc": 13226066032359371072, "features": "[\"optional_balance_check\", \"optional_block_gas_limit\", \"optional_eip3607\", \"optional_no_base_fee\", \"serde\", \"std\"]", "declared_features": "[\"default\", \"dev\", \"memory_limit\", \"optional_balance_check\", \"optional_block_gas_limit\", \"optional_eip3607\", \"optional_no_base_fee\", \"serde\", \"std\"]", "target": 10400323345475179975, "profile": 14841059247222094007, "path": 7622170259055018825, "deps": [[2828590642173593838, "cfg_if", false, 1903128207448990402], [3356788409651158223, "bytecode", false, 9940648042198528172], [4869748615132615553, "context_interface", false, 7152811009136752898], [6908495589658990549, "derive_where", false, 7253175931244881373], [8284619062948117847, "state", false, 16719892843953541142], [9689903380558560274, "serde", false, 6295511631161797798], [9862843292134518369, "database_interface", false, 16486212555994433209], [14539391407805927429, "primitives", false, 14232447812374991554]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/revm-context-869f6b9ef5d73971/dep-lib-revm_context", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}