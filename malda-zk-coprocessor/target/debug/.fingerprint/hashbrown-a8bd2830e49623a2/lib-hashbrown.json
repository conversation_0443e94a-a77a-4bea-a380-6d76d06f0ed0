{"rustc": 13226066032359371072, "features": "[\"allocator-api2\", \"default-hasher\", \"inline-more\", \"serde\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 15657897354478470176, "path": 4527459610340134764, "deps": [[9150530836556604396, "allocator_api2", false, 9347519979546275768], [9689903380558560274, "serde", false, 6295511631161797798], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 15244818623633299663]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-a8bd2830e49623a2/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}