{"rustc": 13226066032359371072, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1590474736902320525, "build_script_build", false, 17160676151539024662]], "local": [{"RerunIfChanged": {"output": "debug/build/methods-303528c7a1db7099/output", "paths": ["/home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/debug/build/methods-303528c7a1db7099/out/methods.rs"]}}, {"RerunIfEnvChanged": {"var": "RISC0_BUILD_DEBUG", "val": null}}, {"RerunIfEnvChanged": {"var": "RISC0_SKIP_BUILD", "val": null}}, {"RerunIfEnvChanged": {"var": "RISC0_BUILD_LOCKED", "val": null}}, {"RerunIfEnvChanged": {"var": "RISC0_RUST_SRC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_riscv32im_risc0_zkvm_elf", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_riscv32im_risc0_zkvm_elf", "val": null}}, {"RerunIfEnvChanged": {"var": "RISC0_BUILD_DEBUG", "val": null}}, {"RerunIfEnvChanged": {"var": "RISC0_SKIP_BUILD", "val": null}}, {"RerunIfEnvChanged": {"var": "RISC0_SKIP_BUILD", "val": null}}, {"RerunIfEnvChanged": {"var": "RISC0_GUEST_LOGFILE", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}