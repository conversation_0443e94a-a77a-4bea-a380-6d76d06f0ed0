{"$message_type":"diagnostic","message":"unused import: `signature_from_bytes`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"malda_utils/src/vulnerability_test.rs","byte_start":174,"byte_end":194,"line_start":5,"line_end":5,"column_start":43,"column_end":63,"is_primary":true,"text":[{"text":"use crate::cryptography::{recover_signer, signature_from_bytes};","highlight_start":43,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"malda_utils/src/vulnerability_test.rs","byte_start":172,"byte_end":194,"line_start":5,"line_end":5,"column_start":41,"column_end":63,"is_primary":true,"text":[{"text":"use crate::cryptography::{recover_signer, signature_from_bytes};","highlight_start":41,"highlight_end":63}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"malda_utils/src/vulnerability_test.rs","byte_start":157,"byte_end":158,"line_start":5,"line_end":5,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use crate::cryptography::{recover_signer, signature_from_bytes};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"malda_utils/src/vulnerability_test.rs","byte_start":194,"byte_end":195,"line_start":5,"line_end":5,"column_start":63,"column_end":64,"is_primary":true,"text":[{"text":"use crate::cryptography::{recover_signer, signature_from_bytes};","highlight_start":63,"highlight_end":64}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `signature_from_bytes`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mmalda_utils/src/vulnerability_test.rs:5:43\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::cryptography::{recover_signer, signature_from_bytes};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Bytes`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"malda_utils/src/vulnerability_test.rs","byte_start":263,"byte_end":268,"line_start":6,"line_end":6,"column_start":67,"column_end":72,"is_primary":true,"text":[{"text":"use alloy_primitives::{Address, Signature, B256, U256, keccak256, Bytes};","highlight_start":67,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"malda_utils/src/vulnerability_test.rs","byte_start":261,"byte_end":268,"line_start":6,"line_end":6,"column_start":65,"column_end":72,"is_primary":true,"text":[{"text":"use alloy_primitives::{Address, Signature, B256, U256, keccak256, Bytes};","highlight_start":65,"highlight_end":72}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Bytes`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mmalda_utils/src/vulnerability_test.rs:6:67\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse alloy_primitives::{Address, Signature, B256, U256, keccak256, Bytes};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"2 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 2 warnings emitted\u001b[0m\n\n"}
