{"rustc": 13226066032359371072, "features": "[\"default\", \"op\", \"op-alloy-consensus\", \"op-revm\", \"std\"]", "declared_features": "[\"default\", \"op\", \"op-alloy-consensus\", \"op-revm\", \"secp256k1\", \"std\"]", "target": 5176978098157706261, "profile": 11083538769332601034, "path": 8466379190618631504, "deps": [[156817798628101091, "revm", false, 16820948460758939339], [3635609799059383815, "alloy_sol_types", false, 2320068993150984763], [4552780081777059061, "alloy_primitives", false, 1708916969974984938], [6076877872687043960, "op_revm", false, 16560844250746806573], [8844834803788054349, "alloy_eips", false, 7134968047005548121], [10806645703491011684, "thiserror", false, 16435724550757905811], [11293676373856528358, "derive_more", false, 6031128645935574517], [11709604483720470746, "op_alloy_consensus", false, 3425411148843133054], [15931160604212504468, "alloy_consensus", false, 6025113663526452639], [16453484564107164156, "alloy_hardforks", false, 14495106050747393511], [18125022703902813197, "auto_impl", false, 7779807868399602672]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-evm-dc9819a69d3cee13/dep-lib-alloy_evm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}