{"rustc": 13226066032359371072, "features": "[\"__rustls-tls\", \"data-encoding\", \"handshake\", \"http\", \"httparse\", \"rustls\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 15657897354478470176, "path": 4238725293799765634, "deps": [[99287295355353247, "data_encoding", false, 11470009270974905012], [3150220818285335163, "url", false, 14930828032240483960], [3712811570531045576, "byteorder", false, 6894596790329543132], [4359956005902820838, "utf8", false, 9876509928884779877], [4405182208873388884, "http", false, 7754831301747032745], [5986029879202738730, "log", false, 1690976416000601508], [6163892036024256188, "httparse", false, 946295791862142201], [8008191657135824715, "thiserror", false, 7776962857085638134], [10724389056617919257, "sha1", false, 7301026090310933041], [11295624341523567602, "rustls", false, 9315527580932235824], [13208667028893622512, "rand", false, 13395923389046576688], [16066129441945555748, "bytes", false, 17791678820998801156]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tungstenite-8e0e9976070a9b97/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}