{"rustc": 13226066032359371072, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"once_cell\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"internal_benches\", \"once_cell\", \"slow_tests\", \"std\", \"test_logging\", \"wasm32_c\"]", "target": 17591616432441575691, "profile": 15657897354478470176, "path": 7304506354440905509, "deps": [[2317793503723491507, "untrusted", false, 5367675280900814177], [3016319839805820069, "build_script_build", false, 1963471714618190241], [3722963349756955755, "once_cell", false, 12396378922497620399], [5330658427305787935, "libc", false, 13924901093407034193], [9009208741846480474, "spin", false, 11593107224418052194]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ring-6d9c126729dd40be/dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}