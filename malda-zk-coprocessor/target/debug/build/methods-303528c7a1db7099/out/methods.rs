pub const GET_PROOF_DATA_ELF: &[u8] = include_bytes!("/home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/riscv-guest/methods/guests/riscv32im-risc0-zkvm-elf/release/get-proof-data.bin");
pub const GET_PROOF_DATA_PATH: &str = "/home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/riscv-guest/methods/guests/riscv32im-risc0-zkvm-elf/release/get-proof-data.bin";
pub const GET_PROOF_DATA_ID: [u32; 8] = [3235663076, 3264976056, 2142967563, 324070212, 318317079, 3541753813, 918275307, 2790436942];
