cargo:rerun-if-env-changed=RISC0_BUILD_DEBUG
Building guest package: methods.guests
cargo:rerun-if-env-changed=RISC0_SKIP_BUILD
Using Rust toolchain version 1.88.0
Using rustc: /home/<USER>/.risc0/toolchains/v1.88.0-rust-x86_64-unknown-linux-gnu/bin/rustc
cargo:rerun-if-env-changed=RISC0_BUILD_LOCKED
cargo:rerun-if-env-changed=RISC0_RUST_SRC
cargo:rerun-if-env-changed=CC_riscv32im_risc0_zkvm_elf
cargo:rerun-if-env-changed=CFLAGS_riscv32im_risc0_zkvm_elf
cargo:rerun-if-env-changed=RISC0_BUILD_DEBUG
cargo:rerun-if-env-changed=RISC0_SKIP_BUILD
cargo:rerun-if-env-changed=RISC0_SKIP_BUILD
cargo:rerun-if-changed=/home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/debug/build/methods-303528c7a1db7099/out/methods.rs
cargo:rerun-if-env-changed=RISC0_GUEST_LOGFILE
Copied ELF file from /home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/riscv-guest/methods/guests/riscv32im-risc0-zkvm-elf/release/get-proof-data.bin to /home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/malda_rs/bin/get-proof-data.bin
