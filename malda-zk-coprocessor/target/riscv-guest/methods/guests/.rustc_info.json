{"rustc_fingerprint": 15286576259964001564, "outputs": {"7971740275564407648": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.so\nlib___.so\nlib___.a\nlib___.so\n/home/<USER>/.risc0/toolchains/v1.88.0-rust-x86_64-unknown-linux-gnu\noff\npacked\nunpacked\n___\ndebug_assertions\nfmt_debug=\"full\"\noverflow_checks\npanic=\"unwind\"\nproc_macro\nrelocation_model=\"pic\"\ntarget_abi=\"\"\ntarget_arch=\"x86_64\"\ntarget_endian=\"little\"\ntarget_env=\"gnu\"\ntarget_family=\"unix\"\ntarget_feature=\"fxsr\"\ntarget_feature=\"sse\"\ntarget_feature=\"sse2\"\ntarget_feature=\"x87\"\ntarget_has_atomic\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"64\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"64\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"linux\"\ntarget_pointer_width=\"64\"\ntarget_thread_local\ntarget_vendor=\"unknown\"\nub_checks\nunix\n", "stderr": ""}, "7545688811622974630": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.a\n/home/<USER>/.risc0/toolchains/v1.88.0-rust-x86_64-unknown-linux-gnu\noff\n___\ndebug_assertions\nfmt_debug=\"full\"\ngetrandom_backend=\"custom\"\noverflow_checks\npanic=\"abort\"\nproc_macro\nrelocation_model=\"static\"\ntarget_abi=\"\"\ntarget_arch=\"riscv32\"\ntarget_endian=\"little\"\ntarget_env=\"\"\ntarget_feature=\"m\"\ntarget_has_atomic\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"64\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"64\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"zkvm\"\ntarget_pointer_width=\"32\"\ntarget_vendor=\"risc0\"\nub_checks\n", "stderr": "warning: dropping unsupported crate type `dylib` for target `riscv32im-risc0-zkvm-elf`\n\nwarning: dropping unsupported crate type `cdylib` for target `riscv32im-risc0-zkvm-elf`\n\nwarning: dropping unsupported crate type `proc-macro` for target `riscv32im-risc0-zkvm-elf`\n\nwarning: 3 warnings emitted\n\n"}, "17747080675513052775": {"success": true, "status": "", "code": 0, "stdout": "rustc 1.88.0-dev (de85b1d3d 2025-06-26)\nbinary: rustc\ncommit-hash: de85b1d3d7f48a865174798819d943994ed23a37\ncommit-date: 2025-06-26\nhost: x86_64-unknown-linux-gnu\nrelease: 1.88.0-dev\nLLVM version: 20.1.5\n", "stderr": ""}}, "successes": {}}