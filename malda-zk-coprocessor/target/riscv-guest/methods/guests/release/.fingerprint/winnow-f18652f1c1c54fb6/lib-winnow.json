{"rustc": 929510403554892037, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 14113089254465536004, "profile": 11322533822032096916, "path": 605242125487369861, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/winnow-f18652f1c1c54fb6/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}