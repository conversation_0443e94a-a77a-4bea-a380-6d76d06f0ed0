{"rustc": 929510403554892037, "features": "[\"raw\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 11322533822032096916, "path": 13026221194038526027, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/hashbrown-cbc8ea1b10c21249/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}