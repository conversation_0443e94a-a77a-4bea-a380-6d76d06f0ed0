{"rustc": 929510403554892037, "features": "[\"<PERSON><PERSON>\", \"Copy\", \"Debug\", \"Default\", \"Deref\", \"DerefMut\", \"Eq\", \"Hash\", \"Into\", \"Ord\", \"PartialEq\", \"PartialOrd\", \"default\"]", "declared_features": "[\"<PERSON><PERSON>\", \"Copy\", \"Debug\", \"Default\", \"Deref\", \"DerefMut\", \"Eq\", \"Hash\", \"Into\", \"Ord\", \"PartialEq\", \"PartialOrd\", \"default\", \"full\"]", "target": 9536858342250534546, "profile": 11322533822032096916, "path": 13871538158524368924, "deps": [[7890276103475655350, "enum_ordinalize", false, 5476966523682011923], [8986759836770526006, "syn", false, 17326451289135752229], [12410540580958238005, "proc_macro2", false, 5367014223937828155], [16437840124237027127, "quote", false, 16967952040968620001]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/educe-59a85be2d2a919ad/dep-lib-educe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}