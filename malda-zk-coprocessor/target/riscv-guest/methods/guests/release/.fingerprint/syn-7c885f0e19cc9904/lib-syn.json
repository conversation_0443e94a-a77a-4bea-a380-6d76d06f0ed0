{"rustc": 929510403554892037, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 11322533822032096916, "path": 9889131854700541347, "deps": [[10418434610764581512, "unicode_ident", false, 2231224891232690155], [12410540580958238005, "proc_macro2", false, 5367014223937828155], [16437840124237027127, "quote", false, 16967952040968620001]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-7c885f0e19cc9904/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}