{"rustc": 929510403554892037, "features": "[\"default\", \"display\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 2399817270980795558, "path": 14348493586892266117, "deps": [[8986759836770526006, "syn", false, 17326451289135752229], [12410540580958238005, "proc_macro2", false, 5367014223937828155], [16126285161989458480, "unicode_xid", false, 853088753268303569], [16437840124237027127, "quote", false, 16967952040968620001]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/derive_more-impl-42c8eea6afd3209a/dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}