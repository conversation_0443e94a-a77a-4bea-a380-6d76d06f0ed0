{"rustc": 929510403554892037, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 580995597155582518, "profile": 9903846855209494683, "path": 4012753040325575527, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/futures-utils-wasm-b140c960f64194f4/dep-lib-futures_utils_wasm", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}