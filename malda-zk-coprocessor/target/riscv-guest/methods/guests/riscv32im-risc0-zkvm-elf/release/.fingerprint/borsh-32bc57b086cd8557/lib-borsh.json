{"rustc": 929510403554892037, "features": "[\"borsh-derive\", \"derive\"]", "declared_features": "[\"ascii\", \"borsh-derive\", \"bson\", \"bytes\", \"de_strict_order\", \"default\", \"derive\", \"hashbrown\", \"rc\", \"std\", \"unstable__schema\"]", "target": 4760962088884618199, "profile": 9903846855209494683, "path": 1470253117144744720, "deps": [[3871589403455914374, "borsh_derive", false, 537072620537348198], [8304026633254494341, "build_script_build", false, 11248689624611321411]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/borsh-32bc57b086cd8557/dep-lib-borsh", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}