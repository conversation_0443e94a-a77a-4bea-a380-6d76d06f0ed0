{"rustc": 929510403554892037, "features": "[\"custom\", \"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 9903846855209494683, "path": 8856193401207948186, "deps": [[10411997081178400487, "cfg_if", false, 6533595065699024575]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/getrandom-5ae404a5892f354d/dep-lib-getrandom", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}