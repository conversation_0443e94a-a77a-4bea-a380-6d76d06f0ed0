{"rustc": 929510403554892037, "features": "[\"std\"]", "declared_features": "[\"default\", \"prove\", \"std\", \"unstable\"]", "target": 10587424292316905330, "profile": 9903846855209494683, "path": 9929349744196877603, "deps": [[441025702826721426, "num_bigint", false, 6862421828361019396], [530211389790465181, "hex", false, 3558946511323017617], [533142347765177280, "anyhow", false, 10169292584760349005], [647417929892486539, "ark_serialize", false, 13148016181418113651], [2358608249731162897, "risc0_zkp", false, 65970758555361104], [5264059439965767078, "bytemuck", false, 2404291014542173712], [6151811949586245694, "ark_bn254", false, 6528563386862900672], [9246984816025833931, "ark_groth16", false, 5334183852444389215], [11115194146618580017, "stability", false, 8613849441938298774], [11457159213880670435, "risc0_binfmt", false, 10799618942866990982], [17174345729924723953, "serde", false, 10492132556354846065], [17532637862849517517, "ark_ec", false, 4685044554993887618]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/risc0-groth16-3d6283acca03c78d/dep-lib-risc0_groth16", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}