{"$message_type":"diagnostic","message":"unused imports: `BASE_SEPOLIA_CHAIN_ID`, `ETHEREUM_SEPOLIA_CHAIN_ID`, `LINEA_SEPOLIA_CHAIN_ID`, and `OPTIMISM_SEPOLIA_CHAIN_ID`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/get_proof_data.rs","byte_start":841,"byte_end":863,"line_start":21,"line_end":21,"column_start":80,"column_end":102,"is_primary":true,"text":[{"text":"use malda_utils::constants::{LINEA_CHAIN_ID, BASE_CHAIN_ID, ETHEREUM_CHAIN_ID, LINEA_SEPOLIA_CHAIN_ID, BASE_SEPOLIA_CHAIN_ID, ETHEREUM_SEPOLIA_CHAIN_ID, OPTIMISM_CHAIN_ID, OPTIMISM_SEPOLIA_CHAIN_ID};","highlight_start":80,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/get_proof_data.rs","byte_start":865,"byte_end":886,"line_start":21,"line_end":21,"column_start":104,"column_end":125,"is_primary":true,"text":[{"text":"use malda_utils::constants::{LINEA_CHAIN_ID, BASE_CHAIN_ID, ETHEREUM_CHAIN_ID, LINEA_SEPOLIA_CHAIN_ID, BASE_SEPOLIA_CHAIN_ID, ETHEREUM_SEPOLIA_CHAIN_ID, OPTIMISM_CHAIN_ID, OPTIMISM_SEPOLIA_CHAIN_ID};","highlight_start":104,"highlight_end":125}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/get_proof_data.rs","byte_start":888,"byte_end":913,"line_start":21,"line_end":21,"column_start":127,"column_end":152,"is_primary":true,"text":[{"text":"use malda_utils::constants::{LINEA_CHAIN_ID, BASE_CHAIN_ID, ETHEREUM_CHAIN_ID, LINEA_SEPOLIA_CHAIN_ID, BASE_SEPOLIA_CHAIN_ID, ETHEREUM_SEPOLIA_CHAIN_ID, OPTIMISM_CHAIN_ID, OPTIMISM_SEPOLIA_CHAIN_ID};","highlight_start":127,"highlight_end":152}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/get_proof_data.rs","byte_start":934,"byte_end":959,"line_start":21,"line_end":21,"column_start":173,"column_end":198,"is_primary":true,"text":[{"text":"use malda_utils::constants::{LINEA_CHAIN_ID, BASE_CHAIN_ID, ETHEREUM_CHAIN_ID, LINEA_SEPOLIA_CHAIN_ID, BASE_SEPOLIA_CHAIN_ID, ETHEREUM_SEPOLIA_CHAIN_ID, OPTIMISM_CHAIN_ID, OPTIMISM_SEPOLIA_CHAIN_ID};","highlight_start":173,"highlight_end":198}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/bin/get_proof_data.rs","byte_start":839,"byte_end":913,"line_start":21,"line_end":21,"column_start":78,"column_end":152,"is_primary":true,"text":[{"text":"use malda_utils::constants::{LINEA_CHAIN_ID, BASE_CHAIN_ID, ETHEREUM_CHAIN_ID, LINEA_SEPOLIA_CHAIN_ID, BASE_SEPOLIA_CHAIN_ID, ETHEREUM_SEPOLIA_CHAIN_ID, OPTIMISM_CHAIN_ID, OPTIMISM_SEPOLIA_CHAIN_ID};","highlight_start":78,"highlight_end":152}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/bin/get_proof_data.rs","byte_start":932,"byte_end":959,"line_start":21,"line_end":21,"column_start":171,"column_end":198,"is_primary":true,"text":[{"text":"use malda_utils::constants::{LINEA_CHAIN_ID, BASE_CHAIN_ID, ETHEREUM_CHAIN_ID, LINEA_SEPOLIA_CHAIN_ID, BASE_SEPOLIA_CHAIN_ID, ETHEREUM_SEPOLIA_CHAIN_ID, OPTIMISM_CHAIN_ID, OPTIMISM_SEPOLIA_CHAIN_ID};","highlight_start":171,"highlight_end":198}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `BASE_SEPOLIA_CHAIN_ID`, `ETHEREUM_SEPOLIA_CHAIN_ID`, `LINEA_SEPOLIA_CHAIN_ID`, and `OPTIMISM_SEPOLIA_CHAIN_ID`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/get_proof_data.rs:21:80\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mAIN_ID, LINEA_SEPOLIA_CHAIN_ID, BASE_SEPOLIA_CHAIN_ID, ETHEREUM_SEPOLIA_CHAIN_ID, OPTIMISM_CHAIN_ID, OPTIMISM_SEPOLIA_CHAIN_ID};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"1 warning emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 1 warning emitted\u001b[0m\n\n"}
