{"rustc": 929510403554892037, "features": "[\"macros\"]", "declared_features": "[\"bench\", \"datagen\", \"deserialize_bincode_1\", \"deserialize_json\", \"deserialize_postcard_1\", \"experimental\", \"log_error_context\", \"logging\", \"macros\", \"serde\", \"std\", \"sync\"]", "target": *******************, "profile": 9903846855209494683, "path": 9412899782107388595, "deps": [[1218499983858120347, "icu_locid", false, 8324984341748896639], [1525434525203289531, "zerofrom", false, 13427427216392849446], [*******************, "stable_deref_trait", false, 8865494945700737882], [5298260564258778412, "displaydoc", false, 13480662470748501373], [6416053172979116596, "yoke", false, 13184172985060551032], [8375043423015247637, "tinystr", false, 16705457362878589849], [11734826517668253792, "writeable", false, 1013982559283140902], [14072360194315679348, "zerovec", false, 12503118430559753424], [15837516966853249478, "icu_provider_macros", false, 185941054279361920]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/icu_provider-98ab6a2bf9bebe91/dep-lib-icu_provider", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}