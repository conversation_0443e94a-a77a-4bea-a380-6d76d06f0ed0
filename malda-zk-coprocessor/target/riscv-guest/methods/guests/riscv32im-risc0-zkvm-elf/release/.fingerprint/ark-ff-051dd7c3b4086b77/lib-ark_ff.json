{"rustc": 929510403554892037, "features": "[\"asm\", \"std\"]", "declared_features": "[\"asm\", \"default\", \"parallel\", \"rayon\", \"std\"]", "target": 17788059205084102962, "profile": 9903846855209494683, "path": 12604187452996283991, "deps": [[213085045802986828, "educe", false, 16092835176423962811], [441025702826721426, "num_bigint", false, 6862421828361019396], [647417929892486539, "ark_serialize", false, 13148016181418113651], [3317542222502007281, "itertools", false, 6282602620976546974], [4778330735589328161, "arrayvec", false, 17842919483489448339], [5157631553186200874, "num_traits", false, 9080360917113034993], [5474302486298000169, "ark_ff_asm", false, 15333103912217746080], [6528079939221783635, "zeroize", false, 4546107126135387577], [14538162554284365360, "ark_ff_macros", false, 4138181394886967691], [15175849579008230926, "ark_std", false, 17404706650990713256], [17475753849556516473, "digest", false, 17046069751249874857], [17605717126308396068, "paste", false, 5113412669452377635]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/ark-ff-051dd7c3b4086b77/dep-lib-ark_ff", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}