{"rustc": 929510403554892037, "features": "[]", "declared_features": "[\"cuda\", \"default\", \"metal\", \"prove\", \"risc0-circuit-recursion-sys\", \"std\", \"test\"]", "target": 744416552149155838, "profile": 9903846855209494683, "path": 11457346955893701835, "deps": [[533142347765177280, "anyhow", false, 10169292584760349005], [2358608249731162897, "risc0_zkp", false, 65970758555361104], [7264961447180378050, "risc0_core", false, 1286203582814547209], [14626413149905853098, "tracing", false, 15233105079028668215], [16097111343857175931, "build_script_build", false, 3865211341073904081]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/risc0-circuit-recursion-4d959fdef2654838/dep-lib-risc0_circuit_recursion", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}