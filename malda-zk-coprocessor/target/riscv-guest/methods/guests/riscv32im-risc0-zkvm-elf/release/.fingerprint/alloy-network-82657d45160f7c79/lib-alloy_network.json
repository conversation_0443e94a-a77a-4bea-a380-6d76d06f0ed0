{"rustc": 929510403554892037, "features": "[]", "declared_features": "[\"k256\"]", "target": 14063547271430870162, "profile": 3351299466017750765, "path": 4372659475511370489, "deps": [[702787943357950849, "futures_utils_wasm", false, 7388535319948934662], [2959752630505841847, "alloy_json_rpc", false, 11269055277315668374], [3635609799059383815, "alloy_sol_types", false, 1913580484393623487], [4234611482138378758, "alloy_consensus_any", false, 3241266909896230008], [4552780081777059061, "alloy_primitives", false, 10398177971877800366], [7168537401094488756, "alloy_signer", false, 16067124068775129094], [8387760878452413362, "thiserror", false, 18414837052735707310], [8844834803788054349, "alloy_eips", false, 7721881406054389152], [9216901827022316173, "alloy_rpc_types_any", false, 17576784822857705115], [9890037079353029638, "alloy_serde", false, 12371655828437115264], [11293676373856528358, "derive_more", false, 4817851534440218684], [11841808107544326057, "serde_json", false, 11341782947549932442], [12763872976636226336, "async_trait", false, 5214630975974418214], [15168588510569655375, "alloy_network_primitives", false, 6190451215961718087], [15466475988933116122, "alloy_rpc_types_eth", false, 5571651095750682883], [15931160604212504468, "alloy_consensus", false, 12608816243967906248], [17174345729924723953, "serde", false, 10492132556354846065], [18125022703902813197, "auto_impl", false, 5773491203487097417]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/alloy-network-82657d45160f7c79/dep-lib-alloy_network", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}