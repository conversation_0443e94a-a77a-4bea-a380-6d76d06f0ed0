{"rustc": 929510403554892037, "features": "[\"alloc\", \"arithmetic\", \"der\", \"digest\", \"hazmat\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"signing\", \"spki\", \"std\", \"verifying\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"default\", \"der\", \"dev\", \"digest\", \"hazmat\", \"pem\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"sha2\", \"signing\", \"spki\", \"std\", \"verifying\"]", "target": 5012119522651993362, "profile": 9903846855209494683, "path": 2858049807385838218, "deps": [[4234225094004207019, "rfc6979", false, 996421448407194623], [5844362839343846847, "serdect", false, 3009924609083372933], [10149501514950982522, "elliptic_curve", false, 14335906516972003940], [11285023886693207100, "spki", false, 13883012831742008731], [13895928991373641935, "signature", false, 17603686100037351550], [17475753849556516473, "digest", false, 17046069751249874857], [18174519455635309924, "der", false, 15105375221572364554]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/ecdsa-3184065bde027f4c/dep-lib-ecdsa", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}