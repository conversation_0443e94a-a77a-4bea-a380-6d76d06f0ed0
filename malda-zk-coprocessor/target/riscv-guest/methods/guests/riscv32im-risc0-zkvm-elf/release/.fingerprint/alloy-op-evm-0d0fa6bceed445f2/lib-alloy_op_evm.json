{"rustc": 929510403554892037, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"std\"]", "target": 13929539632713448124, "profile": 4118870830087558887, "path": 3797508364349789310, "deps": [[156817798628101091, "revm", false, 1070825387991743680], [4552780081777059061, "alloy_primitives", false, 10398177971877800366], [6076877872687043960, "op_revm", false, 14963523133235949107], [8844834803788054349, "alloy_eips", false, 7721881406054389152], [11709604483720470746, "op_alloy_consensus", false, 3099191253943402139], [13133677251112020922, "alloy_evm", false, 9894207054591292553], [13746288811162277849, "alloy_op_hardforks", false, 6513248614474475938], [15931160604212504468, "alloy_consensus", false, 12608816243967906248], [18125022703902813197, "auto_impl", false, 5773491203487097417]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/alloy-op-evm-0d0fa6bceed445f2/dep-lib-alloy_op_evm", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}