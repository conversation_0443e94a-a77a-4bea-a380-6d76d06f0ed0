{"rustc": 929510403554892037, "features": "[\"default\", \"ethereum\", \"serde\", \"std\"]", "declared_features": "[\"arbitrary\", \"default\", \"ethereum\", \"serde\", \"std\"]", "target": 14962727802441550522, "profile": 10549601328979255166, "path": 9165718841042518875, "deps": [[4552780081777059061, "alloy_primitives", false, 10398177971877800366], [4778330735589328161, "arrayvec", false, 17842919483489448339], [6831611227313043439, "smallvec", false, 2249730941537842416], [10187814852784007409, "nybbles", false, 4836880538104062210], [11293676373856528358, "derive_more", false, 4817851534440218684], [14626413149905853098, "tracing", false, 15233105079028668215], [15189774839191387575, "alloy_rlp", false, 12951874142843586789], [17174345729924723953, "serde", false, 10492132556354846065]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/alloy-trie-28c0d5045af55598/dep-lib-alloy_trie", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}