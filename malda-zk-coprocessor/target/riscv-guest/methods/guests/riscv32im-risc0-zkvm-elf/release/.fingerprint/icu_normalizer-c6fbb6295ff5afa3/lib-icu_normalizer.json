{"rustc": 929510403554892037, "features": "[\"compiled_data\", \"default\"]", "declared_features": "[\"compiled_data\", \"datagen\", \"default\", \"experimental\", \"serde\", \"std\"]", "target": 10938057327825117163, "profile": 9903846855209494683, "path": 2419658196891654266, "deps": [[2510068781171235157, "icu_provider", false, 16304138714132541367], [4151217541626572533, "utf16_iter", false, 17683505559705608534], [5078124415930854154, "utf8_iter", false, 12013811801021423691], [5298260564258778412, "displaydoc", false, 13480662470748501373], [6831611227313043439, "smallvec", false, 2249730941537842416], [9343910533247851620, "icu_collections", false, 14402944356666394278], [11287875276472097183, "icu_properties", false, 9644788557972552147], [14072360194315679348, "zerovec", false, 12503118430559753424], [16363175598792086436, "write16", false, 8386750209224617342], [16559649889751396768, "icu_normalizer_data", false, 11238961493189009725]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/icu_normalizer-c6fbb6295ff5afa3/dep-lib-icu_normalizer", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}