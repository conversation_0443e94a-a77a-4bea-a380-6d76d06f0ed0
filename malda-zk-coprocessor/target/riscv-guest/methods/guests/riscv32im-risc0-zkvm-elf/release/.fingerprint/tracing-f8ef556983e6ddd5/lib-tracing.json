{"rustc": 929510403554892037, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 1013435428882189070, "profile": 9903846855209494683, "path": 14836370092958762724, "deps": [[4800206021143169329, "pin_project_lite", false, 14516429197823410938], [5379354381544936779, "log", false, 12198272854187488306], [12756065109715636409, "tracing_core", false, 16634786721825984341], [16410942614558742417, "tracing_attributes", false, 5838301245716184674]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/tracing-f8ef556983e6ddd5/dep-lib-tracing", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}