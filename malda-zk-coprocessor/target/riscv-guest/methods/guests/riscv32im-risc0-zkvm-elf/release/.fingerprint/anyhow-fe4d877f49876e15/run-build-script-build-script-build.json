{"rustc": 929510403554892037, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[533142347765177280, "build_script_build", false, 5423844564164034202]], "local": [{"RerunIfChanged": {"output": "riscv32im-risc0-zkvm-elf/release/build/anyhow-fe4d877f49876e15/output", "paths": ["build/probe.rs"]}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 0, "compile_kind": 0}