{"rustc": 929510403554892037, "features": "[]", "declared_features": "[]", "target": 42855456437065683, "profile": 4605663002376866735, "path": 10523561486186691947, "deps": [[384373354905502547, "risc0_zkvm", false, 16768815747450198707], [3635609799059383815, "alloy_sol_types", false, 1913580484393623487], [3927322629343455429, "malda_utils", false, 8461607936086464576], [3961213746495619226, "risc0_op_steel", false, 4474377614978989128], [4552780081777059061, "alloy_primitives", false, 10398177971877800366], [7496041568175492041, "bls12_381", false, 5791763676027223644], [8837041894380175753, "risc0_steel", false, 9639757487578185782], [9651819995462335867, "tiny_keccak", false, 16786356201051403497], [15931160604212504468, "alloy_consensus", false, 12608816243967906248], [15939406953698110744, "revm", false, 15645146438953347496], [16824809584955268612, "k256", false, 10766864630823199172]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/guests-30e7d9b971df7d86/dep-bin-get-proof-data", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}