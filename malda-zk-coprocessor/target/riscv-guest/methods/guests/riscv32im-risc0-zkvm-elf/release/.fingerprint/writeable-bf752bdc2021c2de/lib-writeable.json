{"rustc": 929510403554892037, "features": "[]", "declared_features": "[\"bench\", \"either\"]", "target": 2501750852737614978, "profile": 9903846855209494683, "path": 9859789050665876986, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/writeable-bf752bdc2021c2de/dep-lib-writeable", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}