{"rustc": 929510403554892037, "features": "[\"allocator-api2\", \"default-hasher\", \"inline-more\", \"serde\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 9903846855209494683, "path": 2533389711682334832, "deps": [[9150530836556604396, "allocator_api2", false, 507297107341629624], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 15362272508840032295], [17174345729924723953, "serde", false, 10492132556354846065]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/hashbrown-6aeb5365778b631f/dep-lib-hashbrown", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}