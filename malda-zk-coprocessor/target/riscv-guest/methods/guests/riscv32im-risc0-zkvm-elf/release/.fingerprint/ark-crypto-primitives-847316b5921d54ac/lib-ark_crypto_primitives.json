{"rustc": 929510403554892037, "features": "[\"merlin\", \"snark\", \"sponge\", \"std\"]", "declared_features": "[\"ark-r1cs-std\", \"commitment\", \"crh\", \"default\", \"encryption\", \"hashbrown\", \"merkle_tree\", \"merlin\", \"parallel\", \"prf\", \"print-trace\", \"r1cs\", \"rayon\", \"signature\", \"snark\", \"sponge\", \"std\", \"tracing\"]", "target": 12232848036435804882, "profile": 9903846855209494683, "path": 5912250112640367307, "deps": [[647417929892486539, "ark_serialize", false, 13148016181418113651], [5236433071915784494, "sha2", false, 9874228177678651115], [5502062331616315784, "ark_ff", false, 6451503171924944143], [8700459469608572718, "blake2", false, 6515535734309412579], [9234201994497484447, "merlin", false, 944250208801126046], [9889883805127379877, "ark_relations", false, 1282463976459927760], [10791833957791020630, "ahash", false, 10527044243846769554], [13859769749131231458, "derivative", false, 3830164068497624011], [14614447123944235085, "ark_crypto_primitives_macros", false, 1741693981516847162], [15175849579008230926, "ark_std", false, 17404706650990713256], [16201565495264925093, "ark_snark", false, 9469807332167513292], [17475753849556516473, "digest", false, 17046069751249874857], [17532637862849517517, "ark_ec", false, 4685044554993887618]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/ark-crypto-primitives-847316b5921d54ac/dep-lib-ark_crypto_primitives", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}