{"rustc": 929510403554892037, "features": "[]", "declared_features": "[\"default\", \"dev\", \"memory_limit\", \"optional_balance_check\", \"optional_block_gas_limit\", \"optional_eip3607\", \"optional_no_base_fee\", \"serde\", \"std\"]", "target": 10400323345475179975, "profile": 12829122721159256495, "path": 15313370548406132657, "deps": [[3356788409651158223, "bytecode", false, 17421909907599846809], [6424036089649184905, "database_interface", false, 4418609239904537367], [7021373875661535844, "derive_where", false, 837746925199462044], [7648739663597286125, "state", false, 15893037038920742300], [10411997081178400487, "cfg_if", false, 6533595065699024575], [14539391407805927429, "primitives", false, 747265832430260153], [15611326416737119815, "context_interface", false, 13950156229143526705]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/revm-context-6b15b9ad7ef56b25/dep-lib-revm_context", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}