{"rustc": 929510403554892037, "features": "[]", "declared_features": "[\"asm-keccak\", \"blst\", \"bn\", \"c-kzg\", \"default\", \"hashbrown\", \"kzg-rs\", \"libsecp256k1\", \"portable\", \"secp256k1\", \"secp256r1\", \"std\"]", "target": 15028308191872412523, "profile": 12829122721159256495, "path": 9610210221563217604, "deps": [[647417929892486539, "ark_serialize", false, 13148016181418113651], [3722963349756955755, "once_cell", false, 4347271377636439181], [5236433071915784494, "sha2", false, 9874228177678651115], [5502062331616315784, "ark_ff", false, 6451503171924944143], [6151811949586245694, "ark_bn254", false, 6528563386862900672], [10411997081178400487, "cfg_if", false, 6533595065699024575], [14539391407805927429, "primitives", false, 747265832430260153], [15583278516016338073, "ark_bls12_381", false, 13803271172845067000], [15603583605579657406, "ripemd", false, 5491249948224246371], [16824809584955268612, "k256", false, 10766864630823199172], [17532637862849517517, "ark_ec", false, 4685044554993887618], [17864845128643396522, "aurora_engine_modexp", false, 4901248370193570451]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/revm-precompile-ffa4a56a5cfdec1b/dep-lib-revm_precompile", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}