{"rustc": 929510403554892037, "features": "[\"alloc\", \"block-buffer\", \"const-oid\", \"core-api\", \"default\", \"mac\", \"oid\", \"std\", \"subtle\"]", "declared_features": "[\"alloc\", \"blobby\", \"block-buffer\", \"const-oid\", \"core-api\", \"default\", \"dev\", \"mac\", \"oid\", \"rand_core\", \"std\", \"subtle\"]", "target": 7510122432137863311, "profile": 9903846855209494683, "path": 15619589820833163478, "deps": [[2352660017780662552, "crypto_common", false, 9255029421030410188], [5343333008895563666, "subtle", false, 10741643817639880440], [8066688306558157009, "const_oid", false, 14812587223171893758], [10626340395483396037, "block_buffer", false, 13148320147796178613]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/digest-082713c9e7ccb6d4/dep-lib-digest", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}