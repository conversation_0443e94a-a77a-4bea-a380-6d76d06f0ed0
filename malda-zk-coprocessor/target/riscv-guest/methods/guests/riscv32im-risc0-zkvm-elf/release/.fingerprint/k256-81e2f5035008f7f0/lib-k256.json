{"rustc": 929510403554892037, "features": "[\"alloc\", \"arithmetic\", \"default\", \"digest\", \"ecdsa\", \"ecdsa-core\", \"expose-field\", \"once_cell\", \"pkcs8\", \"precomputed-tables\", \"schnorr\", \"serde\", \"serdect\", \"sha2\", \"sha256\", \"signature\", \"std\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"bits\", \"critical-section\", \"default\", \"digest\", \"ecdh\", \"ecdsa\", \"ecdsa-core\", \"expose-field\", \"hash2curve\", \"hex-literal\", \"jwk\", \"once_cell\", \"pem\", \"pkcs8\", \"precomputed-tables\", \"schnorr\", \"serde\", \"serdect\", \"sha2\", \"sha256\", \"signature\", \"std\", \"test-vectors\"]", "target": 606099465376125212, "profile": 9903846855209494683, "path": 15980754948936614885, "deps": [[2348975382319678783, "ecdsa_core", false, 10500425178381422774], [3722963349756955755, "once_cell", false, 4347271377636439181], [5236433071915784494, "sha2", false, 9874228177678651115], [5264059439965767078, "bytemuck", false, 2404291014542173712], [5844362839343846847, "serdect", false, 3009924609083372933], [6960295053817223842, "risc0_bigint2", false, 13516983336405297333], [10149501514950982522, "elliptic_curve", false, 14335906516972003940], [10411997081178400487, "cfg_if", false, 6533595065699024575], [13895928991373641935, "signature", false, 17603686100037351550]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/k256-81e2f5035008f7f0/dep-lib-k256", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}