{"rustc": 929510403554892037, "features": "[\"alloc\", \"bits\", \"default\", \"group\", \"groups\", \"pairing\", \"pairings\"]", "declared_features": "[\"alloc\", \"bits\", \"default\", \"digest\", \"experimental\", \"group\", \"groups\", \"nightly\", \"pairing\", \"pairings\", \"zeroize\"]", "target": 14172868317399986293, "profile": 9903846855209494683, "path": 15336326872916585135, "deps": [[4629051840990816016, "pairing", false, 7673910534280062083], [5264059439965767078, "bytemuck", false, 2404291014542173712], [5343333008895563666, "subtle", false, 10741643817639880440], [6960295053817223842, "risc0_bigint2", false, 13516983336405297333], [13163366046229301192, "group", false, 3235230723418832677], [15745367031613275553, "ff", false, 3362720530240667227], [18130209639506977569, "rand_core", false, 14881133024665949176]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/bls12_381-e4eb0ec4765da314/dep-lib-bls12_381", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}