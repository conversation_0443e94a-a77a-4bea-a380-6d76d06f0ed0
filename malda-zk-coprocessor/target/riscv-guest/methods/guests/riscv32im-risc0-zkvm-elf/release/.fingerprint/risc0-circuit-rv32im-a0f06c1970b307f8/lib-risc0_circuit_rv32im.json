{"rustc": 929510403554892037, "features": "[]", "declared_features": "[\"cuda\", \"default\", \"execute\", \"prove\", \"std\", \"witgen_debug\"]", "target": 8658883025994028069, "profile": 9903846855209494683, "path": 6334605519403863853, "deps": [[533142347765177280, "anyhow", false, 10169292584760349005], [2358608249731162897, "risc0_zkp", false, 65970758555361104], [5264059439965767078, "bytemuck", false, 2404291014542173712], [7264961447180378050, "risc0_core", false, 1286203582814547209], [11293676373856528358, "derive_more", false, 4817851534440218684], [11457159213880670435, "risc0_binfmt", false, 10799618942866990982], [14626413149905853098, "tracing", false, 15233105079028668215], [17174345729924723953, "serde", false, 10492132556354846065], [17605717126308396068, "paste", false, 5113412669452377635]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/risc0-circuit-rv32im-a0f06c1970b307f8/dep-lib-risc0_circuit_rv32im", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}