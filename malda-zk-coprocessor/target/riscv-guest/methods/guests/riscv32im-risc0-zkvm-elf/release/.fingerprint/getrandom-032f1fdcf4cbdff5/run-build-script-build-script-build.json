{"rustc": 929510403554892037, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[5010121536552982683, "build_script_build", false, 8679265488402176466]], "local": [{"RerunIfChanged": {"output": "riscv32im-risc0-zkvm-elf/release/build/getrandom-032f1fdcf4cbdff5/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 0, "compile_kind": 0}