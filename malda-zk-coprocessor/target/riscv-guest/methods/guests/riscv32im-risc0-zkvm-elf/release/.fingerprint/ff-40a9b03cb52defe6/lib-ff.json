{"rustc": 929510403554892037, "features": "[\"alloc\", \"bits\", \"bitvec\"]", "declared_features": "[\"alloc\", \"bits\", \"bitvec\", \"byteorder\", \"default\", \"derive\", \"derive_bits\", \"ff_derive\", \"std\"]", "target": 8731611455144862167, "profile": 9903846855209494683, "path": 4424864432158021920, "deps": [[5343333008895563666, "subtle", false, 10741643817639880440], [15977791033689874879, "bitvec", false, 13240537378794546261], [18130209639506977569, "rand_core", false, 14881133024665949176]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/ff-40a9b03cb52defe6/dep-lib-ff", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}