{"rustc": 929510403554892037, "features": "[\"serde\", \"std\"]", "declared_features": "[\"blst\", \"bn\", \"c-kzg\", \"default\", \"dev\", \"hashbrown\", \"kzg-rs\", \"memory_limit\", \"optional_balance_check\", \"optional_block_gas_limit\", \"optional_eip3607\", \"optional_no_base_fee\", \"portable\", \"secp256k1\", \"serde\", \"std\"]", "target": 12570470908379947039, "profile": 3417862114060784095, "path": 3782253758250267278, "deps": [[156817798628101091, "revm", false, 1070825387991743680], [3722963349756955755, "once_cell", false, 4347271377636439181], [17174345729924723953, "serde", false, 10492132556354846065], [18125022703902813197, "auto_impl", false, 5773491203487097417]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/op-revm-09c94fe753030405/dep-lib-op_revm", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}