{"rustc": 929510403554892037, "features": "[\"serde\", \"std\"]", "declared_features": "[\"alloy-compat\", \"arbitrary\", \"default\", \"k256\", \"kzg\", \"serde\", \"serde-bincode-compat\", \"serde_with\", \"std\"]", "target": 8737754881920355545, "profile": 3238053561477906317, "path": 15941372583881873649, "deps": [[4552780081777059061, "alloy_primitives", false, 10398177971877800366], [8387760878452413362, "thiserror", false, 18414837052735707310], [8844834803788054349, "alloy_eips", false, 7721881406054389152], [9890037079353029638, "alloy_serde", false, 12371655828437115264], [11293676373856528358, "derive_more", false, 4817851534440218684], [15189774839191387575, "alloy_rlp", false, 12951874142843586789], [15931160604212504468, "alloy_consensus", false, 12608816243967906248], [17174345729924723953, "serde", false, 10492132556354846065]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/op-alloy-consensus-dd50be3926ca9583/dep-lib-op_alloy_consensus", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}