{"rustc": 929510403554892037, "features": "[]", "declared_features": "[\"debug-transcript\", \"default\", \"hex\", \"nightly\", \"std\"]", "target": 5266747247649936540, "profile": 9903846855209494683, "path": 17113834012415419008, "deps": [[3712811570531045576, "byteorder", false, 14149123534548855], [6528079939221783635, "zeroize", false, 4546107126135387577], [13533998206189078432, "keccak", false, 2673130973890496400], [18130209639506977569, "rand_core", false, 14881133024665949176]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/merlin-84c302cd1bfd02dd/dep-lib-merlin", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}