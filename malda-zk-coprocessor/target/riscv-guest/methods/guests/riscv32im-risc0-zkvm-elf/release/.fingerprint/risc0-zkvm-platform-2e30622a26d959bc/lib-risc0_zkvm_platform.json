{"rustc": 929510403554892037, "features": "[\"export-getrandom\", \"export-libm\", \"export-syscalls\", \"rust-runtime\", \"unstable\"]", "declared_features": "[\"default\", \"entrypoint\", \"export-getrandom\", \"export-libm\", \"export-syscalls\", \"getrandom\", \"heap-embedded-alloc\", \"panic-handler\", \"rust-runtime\", \"sys-args\", \"sys-getenv\", \"unstable\"]", "target": 14194622154004939426, "profile": 9903846855209494683, "path": 10844474889077021829, "deps": [[5010121536552982683, "getrandom", false, 4362373276507191256], [5264059439965767078, "bytemuck", false, 2404291014542173712], [7670211519503158651, "getrandom_v2", false, 9036501551266189909], [10411997081178400487, "cfg_if", false, 6533595065699024575], [11115194146618580017, "stability", false, 8613849441938298774], [12705825803163126369, "libm", false, 14328262407114219780]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/risc0-zkvm-platform-2e30622a26d959bc/dep-lib-risc0_zkvm_platform", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}