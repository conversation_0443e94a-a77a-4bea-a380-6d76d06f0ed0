{"rustc": 929510403554892037, "features": "[\"optional_balance_check\", \"optional_block_gas_limit\", \"optional_eip3607\", \"optional_no_base_fee\", \"serde\", \"std\"]", "declared_features": "[\"default\", \"dev\", \"memory_limit\", \"optional_balance_check\", \"optional_block_gas_limit\", \"optional_eip3607\", \"optional_no_base_fee\", \"serde\", \"std\"]", "target": 10400323345475179975, "profile": 12829122721159256495, "path": 7622170259055018825, "deps": [[3356788409651158223, "bytecode", false, 17421909907599846809], [4869748615132615553, "context_interface", false, 15277678799163092520], [7021373875661535844, "derive_where", false, 837746925199462044], [8284619062948117847, "state", false, 5746945050013579039], [9862843292134518369, "database_interface", false, 6529419717824171851], [10411997081178400487, "cfg_if", false, 6533595065699024575], [14539391407805927429, "primitives", false, 747265832430260153], [17174345729924723953, "serde", false, 10492132556354846065]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/revm-context-f6b829c9e1f62ed5/dep-lib-revm_context", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}