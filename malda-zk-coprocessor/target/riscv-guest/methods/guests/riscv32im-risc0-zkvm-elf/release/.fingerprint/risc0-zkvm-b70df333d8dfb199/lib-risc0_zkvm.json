{"rustc": 929510403554892037, "features": "[\"std\", \"unstable\"]", "declared_features": "[\"bonsai\", \"circuit_debug\", \"client\", \"cuda\", \"default\", \"disable-dev-mode\", \"docker\", \"dual\", \"getrandom\", \"heap-embedded-alloc\", \"metal\", \"prove\", \"r0vm-ver-compat\", \"redis\", \"std\", \"unstable\", \"witgen_debug\"]", "target": 12664587508387259827, "profile": 9903846855209494683, "path": 7027513431656007094, "deps": [[530211389790465181, "hex", false, 3558946511323017617], [533142347765177280, "anyhow", false, 10169292584760349005], [1467157380972495002, "risc0_circuit_rv32im", false, 15136192600850503076], [1491261457040613773, "risc0_groth16", false, 4375132578483444251], [2186701981143735272, "rrs_lib", false, 10483945180811601313], [2358608249731162897, "risc0_zkp", false, 65970758555361104], [2553907554795296370, "risc0_zkos_v1compat", false, 1111039357501555746], [3851720982022213547, "semver", false, 14141226122532043437], [5264059439965767078, "bytemuck", false, 2404291014542173712], [7264961447180378050, "risc0_core", false, 1286203582814547209], [7670211519503158651, "getrandom", false, 9036501551266189909], [8304026633254494341, "borsh", false, 11758819164987226972], [11109039379400878162, "risc0_zkvm_platform", false, 6368572963662088583], [11115194146618580017, "stability", false, 8613849441938298774], [11293676373856528358, "derive_more", false, 4817851534440218684], [11457159213880670435, "risc0_binfmt", false, 10799618942866990982], [14626413149905853098, "tracing", false, 15233105079028668215], [15839183953079716681, "risc0_circuit_keccak", false, 9875825215795771416], [16097111343857175931, "risc0_circuit_recursion", false, 6433320388908251982], [17174345729924723953, "serde", false, 10492132556354846065]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/risc0-zkvm-b70df333d8dfb199/dep-lib-risc0_zkvm", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}