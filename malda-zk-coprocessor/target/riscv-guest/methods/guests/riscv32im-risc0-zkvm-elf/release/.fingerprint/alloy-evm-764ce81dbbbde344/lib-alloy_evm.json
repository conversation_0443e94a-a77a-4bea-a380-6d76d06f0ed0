{"rustc": 929510403554892037, "features": "[\"default\", \"op\", \"op-alloy-consensus\", \"op-revm\", \"std\"]", "declared_features": "[\"default\", \"op\", \"op-alloy-consensus\", \"op-revm\", \"secp256k1\", \"std\"]", "target": 5176978098157706261, "profile": 4118870830087558887, "path": 8466379190618631504, "deps": [[156817798628101091, "revm", false, 1070825387991743680], [3635609799059383815, "alloy_sol_types", false, 1913580484393623487], [4552780081777059061, "alloy_primitives", false, 10398177971877800366], [6076877872687043960, "op_revm", false, 14963523133235949107], [8387760878452413362, "thiserror", false, 18414837052735707310], [8844834803788054349, "alloy_eips", false, 7721881406054389152], [11293676373856528358, "derive_more", false, 4817851534440218684], [11709604483720470746, "op_alloy_consensus", false, 3099191253943402139], [15931160604212504468, "alloy_consensus", false, 12608816243967906248], [16453484564107164156, "alloy_hardforks", false, 187523125116824509], [18125022703902813197, "auto_impl", false, 5773491203487097417]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/alloy-evm-764ce81dbbbde344/dep-lib-alloy_evm", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}