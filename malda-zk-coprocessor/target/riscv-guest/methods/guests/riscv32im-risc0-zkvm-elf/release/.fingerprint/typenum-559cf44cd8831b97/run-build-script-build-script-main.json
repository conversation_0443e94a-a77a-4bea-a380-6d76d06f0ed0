{"rustc": 929510403554892037, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8722757871864480630, "build_script_main", false, 17266307454534109164]], "local": [{"RerunIfChanged": {"output": "riscv32im-risc0-zkvm-elf/release/build/typenum-559cf44cd8831b97/output", "paths": ["build/main.rs"]}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 0, "compile_kind": 0}