{"rustc": 929510403554892037, "features": "[]", "declared_features": "[\"arbitrary\", \"default\", \"k256\", \"serde\", \"std\"]", "target": 17066601904469602418, "profile": 3238053561477906317, "path": 17701411052965628905, "deps": [[4552780081777059061, "alloy_primitives", false, 10398177971877800366], [8387760878452413362, "thiserror", false, 18414837052735707310], [8844834803788054349, "alloy_eips", false, 7721881406054389152], [9890037079353029638, "alloy_serde", false, 12371655828437115264], [11293676373856528358, "derive_more", false, 4817851534440218684], [11709604483720470746, "op_alloy_consensus", false, 3099191253943402139], [11841808107544326057, "serde_json", false, 11341782947549932442], [15168588510569655375, "alloy_network_primitives", false, 6190451215961718087], [15466475988933116122, "alloy_rpc_types_eth", false, 5571651095750682883], [15931160604212504468, "alloy_consensus", false, 12608816243967906248], [17174345729924723953, "serde", false, 10492132556354846065]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/op-alloy-rpc-types-fe63fa48936693d1/dep-lib-op_alloy_rpc_types", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}