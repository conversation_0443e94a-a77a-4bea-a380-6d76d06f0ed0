{"rustc": 929510403554892037, "features": "[\"serde\", \"std\"]", "declared_features": "[\"default\", \"serde\", \"serde-json\", \"std\"]", "target": 7989550107690637832, "profile": 3417862114060784095, "path": 10806046199616291055, "deps": [[3356788409651158223, "bytecode", false, 17421909907599846809], [4869748615132615553, "context_interface", false, 15277678799163092520], [8156936690115526222, "interpreter", false, 4133188948124791934], [8284619062948117847, "state", false, 5746945050013579039], [9862843292134518369, "database_interface", false, 6529419717824171851], [10222557093593435255, "context", false, 17158717292926105361], [11848337178699950223, "precompile", false, 4003916027249616398], [14539391407805927429, "primitives", false, 747265832430260153], [17174345729924723953, "serde", false, 10492132556354846065], [18125022703902813197, "auto_impl", false, 5773491203487097417]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/revm-handler-d8b992cb4137702f/dep-lib-revm_handler", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}