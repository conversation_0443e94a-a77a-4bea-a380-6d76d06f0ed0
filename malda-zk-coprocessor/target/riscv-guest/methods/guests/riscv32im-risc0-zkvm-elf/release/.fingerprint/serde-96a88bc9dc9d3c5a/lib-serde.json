{"rustc": 929510403554892037, "features": "[\"alloc\", \"default\", \"derive\", \"rc\", \"serde_derive\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"derive\", \"rc\", \"serde_derive\", \"std\", \"unstable\"]", "target": 4017624490652051717, "profile": 9903846855209494683, "path": 10453985992274915727, "deps": [[3956770235326413004, "serde_derive", false, 2233034502316338067], [17174345729924723953, "build_script_build", false, 11548251164877108500]], "local": [{"CheckDepInfo": {"dep_info": "riscv32im-risc0-zkvm-elf/release/.fingerprint/serde-96a88bc9dc9d3c5a/dep-lib-serde", "checksum": false}}], "rustflags": ["-C", "passes=lower-atomic", "-C", "link-arg=-Ttext=0x00200800", "-C", "link-arg=--fatal-warnings", "-C", "panic=abort", "--cfg", "getrandom_backend=\"custom\""], "config": 2069994364910194474, "compile_kind": 9781648680163156174}