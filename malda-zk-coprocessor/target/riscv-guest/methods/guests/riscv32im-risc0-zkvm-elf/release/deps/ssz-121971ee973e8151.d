/home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/riscv-guest/methods/guests/riscv32im-risc0-zkvm-elf/release/deps/ssz-121971ee973e8151.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/bitfield.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode/impls.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode/try_from_iter.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/encode.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/encode/impls.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/legacy.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/union_selector.rs

/home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/riscv-guest/methods/guests/riscv32im-risc0-zkvm-elf/release/deps/libssz-121971ee973e8151.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/bitfield.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode/impls.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode/try_from_iter.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/encode.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/encode/impls.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/legacy.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/union_selector.rs

/home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/riscv-guest/methods/guests/riscv32im-risc0-zkvm-elf/release/deps/libssz-121971ee973e8151.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/bitfield.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode/impls.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode/try_from_iter.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/encode.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/encode/impls.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/legacy.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/union_selector.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/bitfield.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode/impls.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/decode/try_from_iter.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/encode.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/encode/impls.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/legacy.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ethereum_ssz-0.9.0/src/union_selector.rs:
