/home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/riscv-guest/methods/guests/riscv32im-risc0-zkvm-elf/release/deps/k256-81e2f5035008f7f0.d: /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/lib.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/affine.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/field.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/mul.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/projective.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/scalar.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/scalar/wide32.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/ecdsa.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr/signing.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr/verifying.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/../README.md /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/field/field_8x32_risc0.rs

/home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/riscv-guest/methods/guests/riscv32im-risc0-zkvm-elf/release/deps/libk256-81e2f5035008f7f0.rlib: /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/lib.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/affine.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/field.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/mul.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/projective.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/scalar.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/scalar/wide32.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/ecdsa.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr/signing.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr/verifying.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/../README.md /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/field/field_8x32_risc0.rs

/home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/target/riscv-guest/methods/guests/riscv32im-risc0-zkvm-elf/release/deps/libk256-81e2f5035008f7f0.rmeta: /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/lib.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/affine.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/field.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/mul.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/projective.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/scalar.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/scalar/wide32.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/ecdsa.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr/signing.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr/verifying.rs /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/../README.md /home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/field/field_8x32_risc0.rs

/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/lib.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/affine.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/field.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/mul.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/projective.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/scalar.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/scalar/wide32.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/ecdsa.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr/signing.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/schnorr/verifying.rs:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/../README.md:
/home/<USER>/.cargo/git/checkouts/rustcrypto-elliptic-curves-a3591ea6690dd99d/7d4a8d6/k256/src/arithmetic/field/field_8x32_risc0.rs:
