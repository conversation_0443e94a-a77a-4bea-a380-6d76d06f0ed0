cargo:rerun-if-changed=build/probe.rs
cargo:rustc-cfg=std_backtrace
cargo:rustc-cfg=error_generic_member_access
cargo:rustc-check-cfg=cfg(anyhow_nightly_testing)
cargo:rustc-check-cfg=cfg(anyhow_no_fmt_arguments_as_str)
cargo:rustc-check-cfg=cfg(anyhow_no_ptr_addr_of)
cargo:rustc-check-cfg=cfg(anyhow_no_unsafe_op_in_unsafe_fn_lint)
cargo:rustc-check-cfg=cfg(doc_cfg)
cargo:rustc-check-cfg=cfg(error_generic_member_access)
cargo:rustc-check-cfg=cfg(std_backtrace)
