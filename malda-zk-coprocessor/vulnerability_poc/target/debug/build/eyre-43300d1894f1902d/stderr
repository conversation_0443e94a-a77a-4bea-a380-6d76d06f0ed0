error[E0407]: method `backtrace` is not a member of trait `Error`
  --> /home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/vulnerability_poc/target/debug/build/eyre-43300d1894f1902d/out/probe.rs:19:9
   |
19 | /         fn backtrace(&self) -> Option<&Backtrace> {
20 | |             let backtrace = Backtrace::capture();
21 | |             match backtrace.status() {
22 | |                 BacktraceStatus::Captured | BacktraceStatus::Disabled | _ => {}
23 | |             }
24 | |             unimplemented!()
25 | |         }
   | |_________^ not a member of trait `Error`

error[E0554]: `#![feature]` may not be used on the stable release channel
 --> /home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/vulnerability_poc/target/debug/build/eyre-43300d1894f1902d/out/probe.rs:2:5
  |
2 |     #![feature(backtrace)]
  |     ^^^^^^^^^^^^^^^^^^^^^^

warning: the feature `backtrace` has been stable since 1.65.0 and no longer requires an attribute to enable
 --> /home/<USER>/2025-07-malda-Maakai123/malda-zk-coprocessor/vulnerability_poc/target/debug/build/eyre-43300d1894f1902d/out/probe.rs:2:16
  |
2 |     #![feature(backtrace)]
  |                ^^^^^^^^^
  |
  = note: `#[warn(stable_features)]` on by default

error: aborting due to 2 previous errors; 1 warning emitted

Some errors have detailed explanations: E0407, E0554.
For more information about an error, try `rustc --explain E0407`.
