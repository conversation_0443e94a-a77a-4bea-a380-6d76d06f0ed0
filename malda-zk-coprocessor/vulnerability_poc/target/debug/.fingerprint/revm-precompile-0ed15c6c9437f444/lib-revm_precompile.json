{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"asm-keccak\", \"blst\", \"bn\", \"c-kzg\", \"default\", \"hashbrown\", \"kzg-rs\", \"libsecp256k1\", \"portable\", \"secp256k1\", \"secp256r1\", \"std\"]", "target": 15028308191872412523, "profile": 14841059247222094007, "path": 9610210221563217604, "deps": [[647417929892486539, "ark_serialize", false, 9604556386554374170], [2828590642173593838, "cfg_if", false, 1903128207448990402], [3434989764622224963, "k256", false, 18351225256497658253], [3722963349756955755, "once_cell", false, 12396378922497620399], [5502062331616315784, "ark_ff", false, 10653404491221717185], [6151811949586245694, "ark_bn254", false, 10584088468334610057], [9857275760291862238, "sha2", false, 14857828535702985917], [14539391407805927429, "primitives", false, 10468590513716982551], [15583278516016338073, "ark_bls12_381", false, 12923086920219799260], [15603583605579657406, "ripemd", false, 1713336919429648377], [15963096839140239429, "aurora_engine_modexp", false, 1083022676480837194], [17532637862849517517, "ark_ec", false, 1952939091342873603]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/revm-precompile-0ed15c6c9437f444/dep-lib-revm_precompile", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}