{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"json\"]", "target": 11427984703986570548, "profile": 10956389272961402606, "path": 11429538825133821388, "deps": [[3060637413840920116, "proc_macro2", false, 11978631202661594756], [4974441333307933176, "syn", false, 14501150983340567860], [12149540829616651437, "alloy_sol_macro_expander", false, 61929790434714287], [15755541468655779741, "proc_macro_error2", false, 18182773156993503925], [17557168633013430082, "alloy_sol_macro_input", false, 9073919672883811983], [17990358020177143287, "quote", false, 4968544993622710939]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-sol-macro-25f61bc90f1ab22e/dep-lib-alloy_sol_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}