{"rustc": 13226066032359371072, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 15657897354478470176, "path": 15508819326427108546, "deps": [[9620753569207166497, "zerovec_derive", false, 1452077850314892817], [10706449961930108323, "yoke", false, 12657147316483062441], [17046516144589451410, "zerofrom", false, 5782657636716892597]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zerovec-9f4cbcefe6fb6e39/dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}