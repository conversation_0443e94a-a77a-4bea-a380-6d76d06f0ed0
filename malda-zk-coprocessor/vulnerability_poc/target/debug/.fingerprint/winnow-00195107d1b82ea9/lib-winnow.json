{"rustc": 13226066032359371072, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 12998675864389268938, "path": 610490136741062626, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-00195107d1b82ea9/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}