{"rustc": 13226066032359371072, "features": "[\"alloc\", \"arithmetic\", \"default\", \"digest\", \"ecdsa\", \"ecdsa-core\", \"expose-field\", \"once_cell\", \"pkcs8\", \"precomputed-tables\", \"schnorr\", \"serde\", \"serdect\", \"sha2\", \"sha256\", \"signature\", \"std\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"bits\", \"critical-section\", \"default\", \"digest\", \"ecdh\", \"ecdsa\", \"ecdsa-core\", \"expose-field\", \"hash2curve\", \"hex-literal\", \"jwk\", \"once_cell\", \"pem\", \"pkcs8\", \"precomputed-tables\", \"schnorr\", \"serde\", \"serdect\", \"sha2\", \"sha256\", \"signature\", \"std\", \"test-vectors\"]", "target": 2074457694779954094, "profile": 15657897354478470176, "path": 11286597672375728731, "deps": [[2348975382319678783, "ecdsa_core", false, 205691285834437682], [2828590642173593838, "cfg_if", false, 1903128207448990402], [3722963349756955755, "once_cell", false, 12396378922497620399], [5844362839343846847, "serdect", false, 1147507532793608039], [9857275760291862238, "sha2", false, 14857828535702985917], [10149501514950982522, "elliptic_curve", false, 1742108654004461897], [13895928991373641935, "signature", false, 12873501524607397721]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/k256-83a7a5690d0ff6b0/dep-lib-k256", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}