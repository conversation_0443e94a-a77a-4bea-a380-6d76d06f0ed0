{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"k256\"]", "target": 14063547271430870162, "profile": 103656143193338361, "path": 11247526112133694115, "deps": [[702787943357950849, "futures_utils_wasm", false, 5892083594587345041], [1236390738802735335, "alloy_sol_types", false, 3651748782085884243], [3524131406528303713, "alloy_eips", false, 10012747206193537287], [4764209563290325147, "alloy_consensus_any", false, 15198836388639424881], [5208208986749300708, "alloy_rpc_types_any", false, 1430756929131410653], [5623574172753995887, "alloy_network_primitives", false, 6267297107205705880], [5988724083763663492, "alloy_primitives", false, 11984454542914676456], [6718263614398771413, "alloy_signer", false, 16663226200415952573], [7657554006239123863, "alloy_json_rpc", false, 248966701204034576], [9689903380558560274, "serde", false, 17336760598612483569], [10806645703491011684, "thiserror", false, 6237539356624216404], [11293676373856528358, "derive_more", false, 5582655070990950649], [11855076707701928598, "alloy_rpc_types_eth", false, 17953123073450980300], [11946729385090170470, "async_trait", false, 6871190887221855849], [16362055519698394275, "serde_json", false, 8472933326026346223], [16980811431682948333, "alloy_consensus", false, 16360848477101045023], [17732661510563293299, "alloy_serde", false, 1805544135948622580], [18125022703902813197, "auto_impl", false, 12467752895867406000]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-network-bf9875205f95a5ef/dep-lib-alloy_network", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}