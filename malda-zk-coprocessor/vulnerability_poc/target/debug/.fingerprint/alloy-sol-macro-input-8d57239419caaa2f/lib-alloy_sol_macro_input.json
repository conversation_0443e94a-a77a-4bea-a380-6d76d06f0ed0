{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"json\"]", "target": 9923371309832013166, "profile": 2225463790103693989, "path": 7307002314969221091, "deps": [[3060637413840920116, "proc_macro2", false, 11978631202661594756], [4974441333307933176, "syn", false, 2401894810052250807], [8501433747651785733, "syn_solidity", false, 7878626473581105559], [11989259058781683633, "dunce", false, 11409768412040215848], [13003293521383684806, "hex", false, 1766313363851940520], [13077543566650298139, "heck", false, 1931619400056872053], [15789864183330730251, "macro_string", false, 9834266371253423045], [17990358020177143287, "quote", false, 4968544993622710939]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-sol-macro-input-8d57239419caaa2f/dep-lib-alloy_sol_macro_input", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}