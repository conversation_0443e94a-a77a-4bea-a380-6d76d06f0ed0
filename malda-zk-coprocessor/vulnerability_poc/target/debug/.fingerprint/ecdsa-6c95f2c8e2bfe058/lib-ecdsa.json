{"rustc": 13226066032359371072, "features": "[\"alloc\", \"arithmetic\", \"der\", \"digest\", \"hazmat\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"signing\", \"spki\", \"std\", \"verifying\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"default\", \"der\", \"dev\", \"digest\", \"hazmat\", \"pem\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"sha2\", \"signing\", \"spki\", \"std\", \"verifying\"]", "target": 5012119522651993362, "profile": 15657897354478470176, "path": 2858049807385838218, "deps": [[4234225094004207019, "rfc6979", false, 15214074557976050430], [5844362839343846847, "serdect", false, 1147507532793608039], [10149501514950982522, "elliptic_curve", false, 1742108654004461897], [10800937535932116261, "der", false, 17825660821718203027], [11285023886693207100, "spki", false, 10875801486941979196], [13895928991373641935, "signature", false, 12873501524607397721], [17475753849556516473, "digest", false, 16399973148787674308]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ecdsa-6c95f2c8e2bfe058/dep-lib-ecdsa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}