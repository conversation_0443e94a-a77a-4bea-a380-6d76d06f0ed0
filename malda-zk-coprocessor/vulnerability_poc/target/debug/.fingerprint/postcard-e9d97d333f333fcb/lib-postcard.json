{"rustc": 13226066032359371072, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"core-num-saturating\", \"crc\", \"default\", \"defmt\", \"embedded-io\", \"embedded-io-04\", \"embedded-io-06\", \"experimental-derive\", \"heapless\", \"heapless-cas\", \"nalgebra-v0_33\", \"nalgebra_v0_33\", \"paste\", \"postcard-derive\", \"use-crc\", \"use-defmt\", \"use-std\"]", "target": 7941872121969890562, "profile": 15657897354478470176, "path": 3078055440812999181, "deps": [[9689903380558560274, "serde", false, 17336760598612483569], [15491115315781000775, "cobs", false, 9355506699016569475]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/postcard-e9d97d333f333fcb/dep-lib-postcard", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}