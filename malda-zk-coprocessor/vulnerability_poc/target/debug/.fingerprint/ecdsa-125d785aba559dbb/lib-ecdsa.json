{"rustc": 13226066032359371072, "features": "[\"alloc\", \"arithmetic\", \"der\", \"digest\", \"hazmat\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"signing\", \"spki\", \"std\", \"verifying\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"default\", \"der\", \"dev\", \"digest\", \"hazmat\", \"pem\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"sha2\", \"signing\", \"spki\", \"std\", \"verifying\"]", "target": 5012119522651993362, "profile": 15657897354478470176, "path": 2858049807385838218, "deps": [[4234225094004207019, "rfc6979", false, 18121591696915894871], [5844362839343846847, "serdect", false, 17819684057387012716], [10149501514950982522, "elliptic_curve", false, 17071433941168514379], [10800937535932116261, "der", false, 3146249800420813791], [11285023886693207100, "spki", false, 4122987302925516060], [13895928991373641935, "signature", false, 5521942856549877155], [17475753849556516473, "digest", false, 17259371155296905814]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ecdsa-125d785aba559dbb/dep-lib-ecdsa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}