{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"json\"]", "target": 11427984703986570548, "profile": 10956389272961402606, "path": 11429538825133821388, "deps": [[3060637413840920116, "proc_macro2", false, 11978631202661594756], [4974441333307933176, "syn", false, 2401894810052250807], [12149540829616651437, "alloy_sol_macro_expander", false, 8383593755960337337], [15755541468655779741, "proc_macro_error2", false, 16897894343402861793], [17557168633013430082, "alloy_sol_macro_input", false, 5305491915392999581], [17990358020177143287, "quote", false, 4968544993622710939]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/alloy-sol-macro-c6866031c87a09cb/dep-lib-alloy_sol_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}