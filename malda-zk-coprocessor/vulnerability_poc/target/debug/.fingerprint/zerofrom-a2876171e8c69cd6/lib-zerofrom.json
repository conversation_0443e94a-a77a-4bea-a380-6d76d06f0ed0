{"rustc": 13226066032359371072, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 15657897354478470176, "path": 1292604811581334034, "deps": [[4022439902832367970, "zerofrom_derive", false, 5828998695468132210]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zerofrom-a2876171e8c69cd6/dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}