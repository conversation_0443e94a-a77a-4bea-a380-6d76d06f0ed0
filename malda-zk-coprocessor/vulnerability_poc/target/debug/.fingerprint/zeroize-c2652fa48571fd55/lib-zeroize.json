{"rustc": 13226066032359371072, "features": "[\"alloc\", \"zeroize_derive\"]", "declared_features": "[\"aarch64\", \"alloc\", \"default\", \"derive\", \"serde\", \"simd\", \"std\", \"zeroize_derive\"]", "target": 12572013220049634676, "profile": 15657897354478470176, "path": 6487618703764803891, "deps": [[15553062592622223563, "zeroize_derive", false, 16282403991382174391]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zeroize-c2652fa48571fd55/dep-lib-zeroize", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}