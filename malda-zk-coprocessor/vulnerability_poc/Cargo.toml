[package]
name = "vulnerability_poc"
version = "0.1.0"
edition = "2021"

[workspace]

[[bin]]
name = "simple_dos_poc"
path = "simple_dos_poc.rs"

[[bin]]
name = "real_function_dos_poc"
path = "real_function_dos_poc.rs"

[[bin]]
name = "vulnerability_poc"
path = "src/main.rs"

[dependencies]
# Dependencies needed to test the actual vulnerability
alloy-primitives = { version = "1.0", features = ["serde"] }
alloy-sol-types = { version = "1.0" }
hex = "0.4"
k256 = { version = "0.13.3", features = ["arithmetic", "serde", "expose-field", "std", "ecdsa"] }
sha3 = "0.10"
