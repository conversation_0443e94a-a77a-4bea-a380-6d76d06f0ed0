/// Precise OpStack Timing Vulnerability Demonstration
/// 
/// This demonstrates the exact vulnerability with controlled timing scenarios

use std::time::{SystemTime, UNIX_EPOCH};

fn main() {
    println!("🚨 OPSTACK TIMING VULNERABILITY - PRECISE DEMONSTRATION");
    println!("=======================================================");
    println!("Location: malda_utils/src/validators.rs:366-370");
    println!();

    demonstrate_vulnerability_precisely();
}

fn demonstrate_vulnerability_precisely() {
    println!("📋 PRECISE VULNERABILITY DEMONSTRATION");
    println!("======================================");
    
    let proof_maturity_delay = 3600u64; // 1 hour for clear demonstration
    let base_time = 1000000u64; // Fixed base timestamp for predictable results
    
    println!("Using proof maturity delay: {} seconds (1 hour)", proof_maturity_delay);
    println!("Base timestamp: {}", base_time);
    println!();

    // Test the exact vulnerability scenario
    println!("🚨 VULNERABILITY SCENARIO");
    println!("-------------------------");
    
    // Game resolved exactly (proof_delay - 300) seconds ago
    // This means 300 seconds remain until proper maturity
    let current_time = base_time + (proof_maturity_delay - 300);
    let resolved_at = base_time;
    let time_elapsed = current_time - resolved_at;
    
    println!("Game resolved at: {}", resolved_at);
    println!("Current time: {}", current_time);
    println!("Time elapsed: {} seconds", time_elapsed);
    println!("Required maturity delay: {} seconds", proof_maturity_delay);
    println!("Time remaining for proper maturity: {} seconds", proof_maturity_delay - time_elapsed);
    println!();
    
    // This is the EXACT vulnerable logic from validators.rs:366-370
    // assert!(current_timestamp - resolved_at > proof_maturity_delay - 300)
    let vulnerable_validation = time_elapsed > (proof_maturity_delay - 300);
    
    // This is what the specification requires
    // assert!(current_timestamp - resolved_at >= proof_maturity_delay)
    let correct_validation = time_elapsed >= proof_maturity_delay;
    
    println!("VALIDATION LOGIC COMPARISON:");
    println!("  Vulnerable: {} > {} = {}", 
        time_elapsed, proof_maturity_delay - 300, vulnerable_validation);
    println!("  Correct:    {} >= {} = {}", 
        time_elapsed, proof_maturity_delay, correct_validation);
    println!();
    
    println!("RESULTS:");
    println!("  Vulnerable implementation: {}", 
        if vulnerable_validation { "✅ PASSES (WRONG!)" } else { "❌ FAILS" });
    println!("  Correct implementation: {}", 
        if correct_validation { "✅ PASSES" } else { "❌ FAILS (CORRECT)" });
    println!();
    
    if vulnerable_validation && !correct_validation {
        println!("🚨 VULNERABILITY CONFIRMED!");
        println!("   The vulnerable code allows validation {} seconds too early!", 
            proof_maturity_delay - time_elapsed);
    } else {
        println!("❌ Vulnerability not demonstrated");
    }
    println!();

    // Test multiple precise scenarios
    println!("🔍 BOUNDARY TESTING");
    println!("-------------------");
    
    let test_cases = vec![
        (proof_maturity_delay, "At exact maturity"),
        (proof_maturity_delay - 1, "1 second early"),
        (proof_maturity_delay - 299, "299 seconds early (edge)"),
        (proof_maturity_delay - 300, "300 seconds early (vulnerability)"),
        (proof_maturity_delay - 301, "301 seconds early (beyond vulnerability)"),
    ];
    
    let mut vulnerability_count = 0;
    
    for (elapsed_time, description) in test_cases {
        let vuln_passes = elapsed_time > (proof_maturity_delay - 300);
        let correct_passes = elapsed_time >= proof_maturity_delay;
        let is_vulnerable = vuln_passes && !correct_passes;
        
        if is_vulnerable {
            vulnerability_count += 1;
        }
        
        println!("  {}: Vuln={}, Correct={} {}", 
            description,
            if vuln_passes { "PASS" } else { "FAIL" },
            if correct_passes { "PASS" } else { "FAIL" },
            if is_vulnerable { "🚨 VULNERABLE" } else { "" }
        );
    }
    
    println!();
    println!("Vulnerabilities found: {}/5 test cases", vulnerability_count);
    println!();

    // Demonstrate the attack window
    println!("⏰ ATTACK WINDOW ANALYSIS");
    println!("-------------------------");
    
    let vulnerability_window_start = proof_maturity_delay - 300;
    let vulnerability_window_end = proof_maturity_delay - 1;
    
    println!("Vulnerability window: {} to {} seconds after resolution", 
        vulnerability_window_start, vulnerability_window_end);
    println!("Window duration: {} seconds (5 minutes)", 
        vulnerability_window_end - vulnerability_window_start + 1);
    println!();
    
    println!("During this window:");
    println!("  • Vulnerable code: ALLOWS validation ✅");
    println!("  • Specification: FORBIDS validation ❌");
    println!("  • Result: SECURITY VIOLATION 🚨");
    println!();

    // Final assessment
    println!("🎯 FINAL ASSESSMENT");
    println!("===================");
    
    if vulnerability_count > 0 {
        println!("✅ VULNERABILITY DEFINITIVELY CONFIRMED");
        println!();
        println!("The vulnerable assertion in validators.rs:366-370:");
        println!("  assert!(current_timestamp - resolved_at > proof_maturity_delay - 300)");
        println!();
        println!("Creates a 300-second (5-minute) window where:");
        println!("  • Proofs are validated BEFORE they should be mature");
        println!("  • This violates the specification requirement");
        println!("  • Attackers can exploit this systematic timing flaw");
        println!();
        println!("🛠️ REQUIRED FIX:");
        println!("  assert!(current_timestamp - resolved_at >= proof_maturity_delay)");
        println!();
        println!("⚡ SEVERITY: CRITICAL - Affects all OpStack chains");
    } else {
        println!("❌ Vulnerability not confirmed in test scenarios");
    }
}

/// Demonstrates the vulnerability with exact code logic
fn demonstrate_exact_code_vulnerability() -> bool {
    // Using the exact same variable names and logic as the vulnerable code
    let proof_maturity_delay = 604800u64; // 7 days (realistic value)
    let current_timestamp = 2000000u64; // Mock current time
    let resolved_at = current_timestamp - (proof_maturity_delay - 300); // Game resolved in vulnerability window
    
    // This is the EXACT vulnerable assertion from validators.rs:366-370
    let vulnerable_assertion = current_timestamp - resolved_at > proof_maturity_delay - 300;
    
    // This is what the specification requires
    let correct_assertion = current_timestamp - resolved_at >= proof_maturity_delay;
    
    println!("🔍 EXACT CODE LOGIC TEST");
    println!("========================");
    println!("current_timestamp: {}", current_timestamp);
    println!("resolved_at: {}", resolved_at);
    println!("proof_maturity_delay: {}", proof_maturity_delay);
    println!();
    println!("Vulnerable assertion: {} - {} > {} - 300", 
        current_timestamp, resolved_at, proof_maturity_delay);
    println!("                     {} > {} = {}", 
        current_timestamp - resolved_at, proof_maturity_delay - 300, vulnerable_assertion);
    println!();
    println!("Correct assertion: {} - {} >= {}", 
        current_timestamp, resolved_at, proof_maturity_delay);
    println!("                  {} >= {} = {}", 
        current_timestamp - resolved_at, proof_maturity_delay, correct_assertion);
    println!();
    
    let vulnerability_exists = vulnerable_assertion && !correct_assertion;
    
    if vulnerability_exists {
        println!("🚨 VULNERABILITY CONFIRMED WITH EXACT CODE LOGIC!");
        println!("   Time advantage: {} seconds", proof_maturity_delay - (current_timestamp - resolved_at));
    } else {
        println!("❌ No vulnerability with exact code logic");
    }
    
    vulnerability_exists
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_exact_vulnerability_logic() {
        // Test with controlled values that should trigger the vulnerability
        let proof_maturity_delay = 3600u64; // 1 hour
        let current_timestamp = 1000000u64;
        let resolved_at = current_timestamp - (proof_maturity_delay - 300); // Exactly in vulnerability window
        
        let time_elapsed = current_timestamp - resolved_at;
        
        // Vulnerable logic (from actual code)
        let vulnerable_passes = time_elapsed > (proof_maturity_delay - 300);
        
        // Correct logic (specification requirement)
        let correct_passes = time_elapsed >= proof_maturity_delay;
        
        println!("Test values:");
        println!("  time_elapsed: {}", time_elapsed);
        println!("  proof_maturity_delay: {}", proof_maturity_delay);
        println!("  vulnerable threshold: {}", proof_maturity_delay - 300);
        println!("  vulnerable_passes: {}", vulnerable_passes);
        println!("  correct_passes: {}", correct_passes);
        
        // The vulnerability exists when vulnerable logic passes but correct logic fails
        assert!(vulnerable_passes, "Vulnerable logic should pass in this scenario");
        assert!(!correct_passes, "Correct logic should fail in this scenario");
        
        println!("✅ Vulnerability confirmed with exact logic!");
    }

    #[test]
    fn test_vulnerability_boundary() {
        let proof_maturity_delay = 3600u64;
        
        // Test exactly at the 300-second boundary
        let time_elapsed = proof_maturity_delay - 300;
        
        let vulnerable_passes = time_elapsed > (proof_maturity_delay - 300);
        let correct_passes = time_elapsed >= proof_maturity_delay;
        
        // At exactly the boundary, vulnerable should fail, correct should fail
        assert!(!vulnerable_passes, "At boundary, vulnerable logic should fail");
        assert!(!correct_passes, "At boundary, correct logic should fail");
        
        // Test 1 second past the boundary (into vulnerability window)
        let time_elapsed_plus_one = proof_maturity_delay - 299;
        
        let vulnerable_passes_plus = time_elapsed_plus_one > (proof_maturity_delay - 300);
        let correct_passes_plus = time_elapsed_plus_one >= proof_maturity_delay;
        
        assert!(vulnerable_passes_plus, "Just past boundary, vulnerable should pass");
        assert!(!correct_passes_plus, "Just past boundary, correct should still fail");
        
        println!("✅ Boundary testing confirms vulnerability window!");
    }
}

// Run the demonstration if not in test mode
#[cfg(not(test))]
fn run_demo() {
    main();
    demonstrate_exact_code_vulnerability();
}
