use malda_utils::cryptography::signature_from_bytes;
use alloy_primitives::Bytes;
use std::panic;

#[test]
#[should_panic(expected = "Invalid signature length")]
fn test_signature_from_bytes_vulnerability_64_bytes() {
    println!("🚨 Testing 64-byte signature vulnerability...");
    let invalid_signature = Bytes::from(vec![0u8; 64]);
    signature_from_bytes(&invalid_signature);
}

#[test]
#[should_panic(expected = "Invalid signature length")]
fn test_signature_from_bytes_vulnerability_66_bytes() {
    println!("🚨 Testing 66-byte signature vulnerability...");
    let invalid_signature = Bytes::from(vec![0u8; 66]);
    signature_from_bytes(&invalid_signature);
}

#[test]
#[should_panic(expected = "Invalid signature length")]
fn test_signature_from_bytes_vulnerability_empty() {
    println!("🚨 Testing empty signature vulnerability...");
    let invalid_signature = Bytes::from(vec![]);
    signature_from_bytes(&invalid_signature);
}

#[test]
#[should_panic(expected = "Invalid signature length")]
fn test_signature_from_bytes_vulnerability_128_bytes() {
    println!("🚨 Testing 128-byte signature vulnerability...");
    let invalid_signature = Bytes::from(vec![0u8; 128]);
    signature_from_bytes(&invalid_signature);
}

#[test]
fn test_signature_from_bytes_valid_65_bytes() {
    println!("✅ Testing valid 65-byte signature...");
    // Create a valid 65-byte signature (r: 32 bytes, s: 32 bytes, v: 1 byte)
    let mut valid_signature = vec![0u8; 64]; // r + s
    valid_signature.push(27); // v (recovery id)
    
    let signature_bytes = Bytes::from(valid_signature);
    
    // This should NOT panic
    let result = panic::catch_unwind(|| {
        signature_from_bytes(&signature_bytes)
    });
    
    assert!(result.is_ok(), "Valid 65-byte signature should not panic");
}

#[test]
fn test_comprehensive_vulnerability_demonstration() {
    println!("🚨 COMPREHENSIVE VULNERABILITY TEST");
    println!("===================================");
    
    let test_cases = vec![
        (0, "Empty signature"),
        (1, "Single byte"),
        (32, "32 bytes (half signature)"),
        (64, "64 bytes (1 short)"),
        (66, "66 bytes (1 over)"),
        (96, "96 bytes (1.5x)"),
        (128, "128 bytes (double)"),
        (255, "255 bytes (large)"),
    ];
    
    let mut vulnerabilities_confirmed = 0;
    
    for (length, description) in test_cases {
        println!("Testing: {} - {}", length, description);
        
        let invalid_signature = Bytes::from(vec![0u8; length]);
        
        let result = panic::catch_unwind(|| {
            signature_from_bytes(&invalid_signature)
        });
        
        match result {
            Ok(_) => {
                println!("  ❌ UNEXPECTED: No panic occurred");
            },
            Err(panic_info) => {
                let panic_msg = panic_info.downcast_ref::<String>()
                    .map(|s| s.as_str())
                    .or_else(|| panic_info.downcast_ref::<&str>().copied())
                    .unwrap_or("Unknown panic");
                
                if panic_msg.contains("Invalid signature length") {
                    println!("  ✅ VULNERABILITY CONFIRMED: {}", panic_msg);
                    vulnerabilities_confirmed += 1;
                } else {
                    println!("  ⚠️  Different error: {}", panic_msg);
                }
            }
        }
    }
    
    println!("===================================");
    println!("VULNERABILITIES CONFIRMED: {}/8", vulnerabilities_confirmed);
    
    // This test should confirm that all non-65-byte signatures trigger the vulnerability
    assert!(vulnerabilities_confirmed >= 7, "Expected at least 7 vulnerabilities to be confirmed");
    
    println!("🚨 CONCLUSION: CRITICAL VULNERABILITY CONFIRMED IN ACTUAL CODE");
    println!("   • signature_from_bytes() panics on invalid lengths");
    println!("   • No error handling - immediate system crash");
    println!("   • 100% reproducible DoS attack vector");
}
